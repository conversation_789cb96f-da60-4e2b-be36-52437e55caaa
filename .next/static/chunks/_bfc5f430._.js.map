{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/src/components/PaymentModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { XMarkIcon, MinusIcon, PlusIcon } from '@heroicons/react/24/outline';\nimport Script from 'next/script';\n\ninterface PaymentModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  selectedPlan: {\n    name: string;\n    basePrice: number;\n    description: string;\n    unit: string;\n  };\n  onPaymentSuccess: (details: any) => void;\n}\n\nconst PaymentModal = ({ isOpen, onClose, selectedPlan, onPaymentSuccess }: PaymentModalProps) => {\n  const [quantity, setQuantity] = useState(1);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [paypalLoaded, setPaypalLoaded] = useState(false);\n\n  useEffect(() => {\n    if (isOpen) {\n      setQuantity(1);\n    }\n  }, [isOpen]);\n\n  useEffect(() => {\n    if (paypalLoaded && isOpen && (window as any).paypal) {\n      initializePayPalButtons();\n    }\n  }, [paypalLoaded, isOpen, quantity]);\n\n  if (!isOpen) return null;\n\n  const minQuantity = 1;\n  const maxQuantity = selectedPlan.unit === 'month' ? 12 : 50;\n\n  const handleQuantityChange = (newQuantity: number) => {\n    if (newQuantity >= minQuantity && newQuantity <= maxQuantity) {\n      setQuantity(newQuantity);\n    }\n  };\n\n  const totalPrice = (selectedPlan.basePrice * quantity).toFixed(2);\n  const pricePerUnit = selectedPlan.basePrice.toFixed(2);\n\n  const initializePayPalButtons = () => {\n    const container = document.getElementById('paypal-button-container');\n    if (container && (window as any).paypal) {\n      container.innerHTML = '';\n\n      (window as any).paypal.Buttons({\n        fundingSource: (window as any).paypal.FUNDING.CARD,\n        style: {\n          layout: 'vertical',\n          shape: 'pill',\n          color: 'blue',\n          label: 'pay'\n        },\n        createOrder: async function(data: any, actions: any) {\n          const response = await fetch('/api/create-order', {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify({\n              plan: `${quantity}x ${selectedPlan.name}`,\n              amount: totalPrice,\n              quantity: quantity\n            })\n          });\n          const orderData = await response.json();\n          return orderData.id;\n        },\n        onApprove: async function(data: any, actions: any) {\n          setIsProcessing(true);\n          try {\n            const response = await fetch(`/api/capture-order/${data.orderID}`, {\n              method: 'POST'\n            });\n            const details = await response.json();\n\n            if (details.status === 'success') {\n              alert('✅ Payment Successful');\n              onPaymentSuccess(details);\n              onClose();\n              window.location.href = '/dashboard?payment=success';\n            }\n          } catch (error) {\n            console.error('Payment error:', error);\n            alert('Payment failed. Please try again.');\n          } finally {\n            setIsProcessing(false);\n          }\n        }\n      }).render('#paypal-button-container');\n    }\n  };\n\n  const getQuantityLabel = () => {\n    if (selectedPlan.unit === 'month') {\n      return quantity === 1 ? '1 Month' : `${quantity} Months`;\n    }\n    return quantity === 1 ? '1 Verification' : `${quantity} Verifications`;\n  };\n\n  return (\n    <>\n      {/* PayPal Script */}\n      <Script\n        src=\"https://www.paypal.com/sdk/js?client-id=AVpJKjl6nABg0Y0Jekdmi58TNAJLbtYsyJ3e9Qruj0hAbK3c4BhZLa6mJWv2lpNLUC_PeTFLsy-vHt-N&components=buttons&enable-funding=card\"\n        onLoad={() => setPaypalLoaded(true)}\n      />\n\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n        <div className=\"bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-xl font-bold text-gray-900\">\n            Purchase {selectedPlan.unit === 'verification' ? 'verifications' : 'subscription'}\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            <XMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          <p className=\"text-gray-600 mb-6 text-center\">\n            Select amount of {selectedPlan.unit === 'verification' ? 'verifications' : 'months'} you need\n          </p>\n\n          {/* Quantity Selector */}\n          <div className=\"flex items-center justify-center mb-6\">\n            <button\n              onClick={() => handleQuantityChange(quantity - 1)}\n              disabled={quantity <= minQuantity}\n              className=\"w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors\"\n            >\n              <MinusIcon className=\"h-5 w-5 text-gray-600\" />\n            </button>\n            \n            <div className=\"mx-8 text-center\">\n              <div className=\"text-3xl font-bold text-gray-900 mb-1\">\n                {quantity} {selectedPlan.unit === 'verification' ? 'Verification' + (quantity > 1 ? 's' : '') : 'Month' + (quantity > 1 ? 's' : '')}\n              </div>\n            </div>\n\n            <button\n              onClick={() => handleQuantityChange(quantity + 1)}\n              disabled={quantity >= maxQuantity}\n              className=\"w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors\"\n            >\n              <PlusIcon className=\"h-5 w-5 text-gray-600\" />\n            </button>\n          </div>\n\n          {/* Quantity Range Slider */}\n          <div className=\"mb-6\">\n            <input\n              type=\"range\"\n              min={minQuantity}\n              max={maxQuantity}\n              value={quantity}\n              onChange={(e) => handleQuantityChange(parseInt(e.target.value))}\n              className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider\"\n              style={{\n                background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${((quantity - minQuantity) / (maxQuantity - minQuantity)) * 100}%, #e5e7eb ${((quantity - minQuantity) / (maxQuantity - minQuantity)) * 100}%, #e5e7eb 100%)`\n              }}\n            />\n            <div className=\"flex justify-between text-sm text-gray-500 mt-2\">\n              <span>{minQuantity}{selectedPlan.unit === 'verification' ? 'V' : 'M'}</span>\n              <span>{maxQuantity}{selectedPlan.unit === 'verification' ? 'V' : 'M'}</span>\n            </div>\n          </div>\n\n          {/* Pricing Details */}\n          <div className=\"bg-gray-50 rounded-xl p-4 mb-6\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <span className=\"text-gray-700\">Price per {selectedPlan.unit}</span>\n              <span className=\"font-semibold\">${pricePerUnit}</span>\n            </div>\n            <div className=\"flex justify-between items-center mb-2\">\n              <span className=\"text-gray-700\">Amount of {selectedPlan.unit === 'verification' ? 'verifications' : 'months'}</span>\n              <span className=\"font-semibold\">{getQuantityLabel()}</span>\n            </div>\n            <hr className=\"my-3 border-gray-200\" />\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-lg font-bold text-gray-900\">Total price:</span>\n              <span className=\"text-lg font-bold text-gray-900\">${totalPrice}</span>\n            </div>\n          </div>\n\n          {/* Payment Section */}\n          {isProcessing ? (\n            <div className=\"flex items-center justify-center py-8\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"></div>\n              <span className=\"text-gray-700\">Processing payment...</span>\n            </div>\n          ) : (\n            <div className=\"mb-6\">\n              <div className=\"text-center mb-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Pay with Card (No PayPal Account Required)</h3>\n                <p className=\"text-sm text-gray-600\">Secure payment powered by PayPal</p>\n              </div>\n              <div id=\"paypal-button-container\" className=\"min-h-[50px]\"></div>\n              {!paypalLoaded && (\n                <div className=\"flex items-center justify-center py-4\">\n                  <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2\"></div>\n                  <span className=\"text-gray-600\">Loading payment options...</span>\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Action Buttons */}\n          <div className=\"flex space-x-3\">\n            <button\n              onClick={onClose}\n              className=\"flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={() => {\n                // This could trigger the PayPal flow or other payment methods\n                console.log(`Continue with ${quantity} ${selectedPlan.unit}(s) for $${totalPrice}`);\n              }}\n              className=\"flex-1 px-4 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors\"\n            >\n              Continue\n            </button>\n          </div>\n\n          {/* Additional Info */}\n          <div className=\"mt-4 text-center\">\n            <p className=\"text-xs text-gray-500\">\n              {selectedPlan.unit === 'verification' \n                ? 'Verifications never expire and can be used anytime'\n                : 'Subscription will auto-renew unless cancelled'\n              }\n            </p>\n          </div>\n        </div>\n      </div>\n      </div>\n    </>\n  );\n};\n\nexport default PaymentModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAkBA,MAAM,eAAe;QAAC,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,gBAAgB,EAAqB;;IAC1F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,QAAQ;gBACV,YAAY;YACd;QACF;iCAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,gBAAgB,UAAU,AAAC,OAAe,MAAM,EAAE;gBACpD;YACF;QACF;iCAAG;QAAC;QAAc;QAAQ;KAAS;IAEnC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc;IACpB,MAAM,cAAc,aAAa,IAAI,KAAK,UAAU,KAAK;IAEzD,MAAM,uBAAuB,CAAC;QAC5B,IAAI,eAAe,eAAe,eAAe,aAAa;YAC5D,YAAY;QACd;IACF;IAEA,MAAM,aAAa,CAAC,aAAa,SAAS,GAAG,QAAQ,EAAE,OAAO,CAAC;IAC/D,MAAM,eAAe,aAAa,SAAS,CAAC,OAAO,CAAC;IAEpD,MAAM,0BAA0B;QAC9B,MAAM,YAAY,SAAS,cAAc,CAAC;QAC1C,IAAI,aAAa,AAAC,OAAe,MAAM,EAAE;YACvC,UAAU,SAAS,GAAG;YAErB,OAAe,MAAM,CAAC,OAAO,CAAC;gBAC7B,eAAe,AAAC,OAAe,MAAM,CAAC,OAAO,CAAC,IAAI;gBAClD,OAAO;oBACL,QAAQ;oBACR,OAAO;oBACP,OAAO;oBACP,OAAO;gBACT;gBACA,aAAa,eAAe,IAAS,EAAE,OAAY;oBACjD,MAAM,WAAW,MAAM,MAAM,qBAAqB;wBAChD,QAAQ;wBACR,SAAS;4BAAE,gBAAgB;wBAAmB;wBAC9C,MAAM,KAAK,SAAS,CAAC;4BACnB,MAAM,AAAC,GAAe,OAAb,UAAS,MAAsB,OAAlB,aAAa,IAAI;4BACvC,QAAQ;4BACR,UAAU;wBACZ;oBACF;oBACA,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,OAAO,UAAU,EAAE;gBACrB;gBACA,WAAW,eAAe,IAAS,EAAE,OAAY;oBAC/C,gBAAgB;oBAChB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,AAAC,sBAAkC,OAAb,KAAK,OAAO,GAAI;4BACjE,QAAQ;wBACV;wBACA,MAAM,UAAU,MAAM,SAAS,IAAI;wBAEnC,IAAI,QAAQ,MAAM,KAAK,WAAW;4BAChC,MAAM;4BACN,iBAAiB;4BACjB;4BACA,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kBAAkB;wBAChC,MAAM;oBACR,SAAU;wBACR,gBAAgB;oBAClB;gBACF;YACF,GAAG,MAAM,CAAC;QACZ;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,aAAa,IAAI,KAAK,SAAS;YACjC,OAAO,aAAa,IAAI,YAAY,AAAC,GAAW,OAAT,UAAS;QAClD;QACA,OAAO,aAAa,IAAI,mBAAmB,AAAC,GAAW,OAAT,UAAS;IACzD;IAEA,qBACE;;0BAEE,6LAAC,iIAAA,CAAA,UAAM;gBACL,KAAI;gBACJ,QAAQ,IAAM,gBAAgB;;;;;;0BAGhC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAkC;wCACpC,aAAa,IAAI,KAAK,iBAAiB,kBAAkB;;;;;;;8CAErE,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAAiC;wCAC1B,aAAa,IAAI,KAAK,iBAAiB,kBAAkB;wCAAS;;;;;;;8CAItF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,qBAAqB,WAAW;4CAC/C,UAAU,YAAY;4CACtB,WAAU;sDAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAGvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;oDACZ;oDAAS;oDAAE,aAAa,IAAI,KAAK,iBAAiB,iBAAiB,CAAC,WAAW,IAAI,MAAM,EAAE,IAAI,UAAU,CAAC,WAAW,IAAI,MAAM,EAAE;;;;;;;;;;;;sDAItI,6LAAC;4CACC,SAAS,IAAM,qBAAqB,WAAW;4CAC/C,UAAU,YAAY;4CACtB,WAAU;sDAEV,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,KAAK;4CACL,KAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK;4CAC7D,WAAU;4CACV,OAAO;gDACL,YAAY,AAAC,iDAA4H,OAA5E,AAAC,CAAC,WAAW,WAAW,IAAI,CAAC,cAAc,WAAW,IAAK,KAAI,eAA4E,OAA/D,AAAC,CAAC,WAAW,WAAW,IAAI,CAAC,cAAc,WAAW,IAAK,KAAI;4CAC1M;;;;;;sDAEF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAM;wDAAa,aAAa,IAAI,KAAK,iBAAiB,MAAM;;;;;;;8DACjE,6LAAC;;wDAAM;wDAAa,aAAa,IAAI,KAAK,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;;8CAKrE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;wDAAgB;wDAAW,aAAa,IAAI;;;;;;;8DAC5D,6LAAC;oDAAK,WAAU;;wDAAgB;wDAAE;;;;;;;;;;;;;sDAEpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;wDAAgB;wDAAW,aAAa,IAAI,KAAK,iBAAiB,kBAAkB;;;;;;;8DACpG,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;;;;;;;sDAEnC,6LAAC;4CAAG,WAAU;;;;;;sDACd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;8DAClD,6LAAC;oDAAK,WAAU;;wDAAkC;wDAAE;;;;;;;;;;;;;;;;;;;gCAKvD,6BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;6FAGlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC;4CAAI,IAAG;4CAA0B,WAAU;;;;;;wCAC3C,CAAC,8BACA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAOxC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,SAAS;gDACP,8DAA8D;gDAC9D,QAAQ,GAAG,CAAC,AAAC,iBAA4B,OAAZ,UAAS,KAAgC,OAA7B,aAAa,IAAI,EAAC,aAAsB,OAAX;4CACxE;4CACA,WAAU;sDACX;;;;;;;;;;;;8CAMH,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDACV,aAAa,IAAI,KAAK,iBACnB,uDACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GA1OM;KAAA;uCA4OS", "debugId": null}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/src/app/checkout/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { CheckIcon, ShieldCheckIcon } from '@heroicons/react/24/solid';\nimport PaymentModal from '@/components/PaymentModal';\n\nconst CheckoutPage = () => {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const [selectedPlan, setSelectedPlan] = useState('basic');\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n\n  const plans = {\n    basic: {\n      name: \"Basic Check\",\n      price: \"9.99\",\n      basePrice: 9.99,\n      unit: \"verification\",\n      description: \"Perfect for individual verification needs\",\n      features: [\n        \"Identity verification\",\n        \"Basic background check\",\n        \"Email support\",\n        \"24-hour processing\",\n        \"Secure document upload\",\n        \"PDF report delivery\"\n      ]\n    },\n    deep: {\n      name: \"Deep ID Scan\",\n      price: \"24.99\",\n      basePrice: 24.99,\n      unit: \"verification\",\n      description: \"Comprehensive verification for businesses\",\n      features: [\n        \"Everything in Basic Check\",\n        \"Advanced background screening\",\n        \"Criminal record check\",\n        \"Employment verification\",\n        \"Priority support\",\n        \"1-hour processing\",\n        \"API access\",\n        \"Bulk upload capability\"\n      ]\n    },\n    unlimited: {\n      name: \"Monthly Unlimited Pass\",\n      price: \"49.99\",\n      basePrice: 49.99,\n      unit: \"month\",\n      description: \"Unlimited verifications for growing businesses\",\n      features: [\n        \"Everything in Deep ID Scan\",\n        \"Unlimited verifications\",\n        \"Real-time processing\",\n        \"Dedicated account manager\",\n        \"Custom integrations\",\n        \"White-label reports\",\n        \"Advanced analytics\",\n        \"Phone support\",\n        \"SLA guarantee\"\n      ]\n    }\n  };\n\n  useEffect(() => {\n    const plan = searchParams.get('plan');\n    if (plan && plans[plan as keyof typeof plans]) {\n      setSelectedPlan(plan);\n    }\n  }, [searchParams]);\n\n  const currentPlan = plans[selectedPlan as keyof typeof plans];\n\n  const handlePaymentSuccess = (details: any) => {\n    console.log('Payment successful:', details);\n    setIsProcessing(true);\n\n    // Simulate API call to process the payment\n    setTimeout(() => {\n      setIsProcessing(false);\n      // Redirect to dashboard with success message\n      router.push('/dashboard?payment=success');\n    }, 2000);\n  };\n\n  const openPaymentModal = () => {\n    setIsModalOpen(true);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-12\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <div className=\"flex justify-center mb-4\">\n            <ShieldCheckIcon className=\"h-12 w-12 text-blue-600\" />\n          </div>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Complete Your Purchase</h1>\n          <p className=\"text-lg text-gray-600\">\n            Secure checkout powered by PayPal with guest payment options\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Plan Selection */}\n          <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Select Your Plan</h2>\n            \n            <div className=\"space-y-4\">\n              {Object.entries(plans).map(([key, plan]) => (\n                <div\n                  key={key}\n                  className={`border-2 rounded-xl p-4 cursor-pointer transition-colors duration-200 ${\n                    selectedPlan === key\n                      ? 'border-blue-600 bg-blue-50'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }`}\n                  onClick={() => setSelectedPlan(key)}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900\">{plan.name}</h3>\n                      <p className=\"text-gray-600\">{plan.description}</p>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-2xl font-bold text-gray-900\">\n                        ${plan.price}\n                        {key === 'unlimited' && (\n                          <span className=\"text-sm text-gray-600 font-normal\">/month</span>\n                        )}\n                      </div>\n                      <div className={`w-4 h-4 rounded-full border-2 mt-2 ${\n                        selectedPlan === key\n                          ? 'bg-blue-600 border-blue-600'\n                          : 'border-gray-300'\n                      }`}>\n                        {selectedPlan === key && (\n                          <CheckIcon className=\"h-3 w-3 text-white m-0.5\" />\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Selected Plan Features */}\n            <div className=\"mt-8 p-6 bg-gray-50 rounded-xl\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                {currentPlan.name} includes:\n              </h3>\n              <ul className=\"space-y-2\">\n                {currentPlan.features.map((feature, index) => (\n                  <li key={index} className=\"flex items-center space-x-2\">\n                    <CheckIcon className=\"h-4 w-4 text-green-500 flex-shrink-0\" />\n                    <span className=\"text-gray-700\">{feature}</span>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n\n          {/* Payment Section */}\n          <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Payment Details</h2>\n            \n            {/* Order Summary */}\n            <div className=\"bg-gray-50 rounded-xl p-6 mb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Order Summary</h3>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-gray-700\">{currentPlan.name}</span>\n                <span className=\"font-semibold\">${currentPlan.price}</span>\n              </div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-gray-700\">Processing Fee</span>\n                <span className=\"font-semibold\">$0.00</span>\n              </div>\n              <hr className=\"my-4\" />\n              <div className=\"flex justify-between items-center text-lg font-bold\">\n                <span>Total</span>\n                <span>${currentPlan.price} USD</span>\n              </div>\n            </div>\n\n            {/* Payment Section */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Payment Method</h3>\n\n              {isProcessing ? (\n                <div className=\"flex items-center justify-center py-8\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"></div>\n                  <span className=\"text-gray-700\">Processing payment...</span>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  <button\n                    onClick={openPaymentModal}\n                    className=\"w-full bg-blue-600 text-white py-4 px-6 rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200 text-lg\"\n                  >\n                    Pay with Card (No PayPal Account Required)\n                  </button>\n                  <p className=\"text-sm text-gray-600 text-center\">\n                    Customize your purchase quantity and pay securely with debit/credit card\n                  </p>\n                  <div className=\"flex items-center justify-center space-x-2 text-xs text-gray-500\">\n                    <span>🔒 SSL Secured</span>\n                    <span>•</span>\n                    <span>💳 Guest Checkout</span>\n                    <span>•</span>\n                    <span>⚡ Instant Access</span>\n                  </div>\n                </div>\n              )}\n\n              {/* Security Badges */}\n              <div className=\"mt-6 pt-6 border-t border-gray-200\">\n                <div className=\"flex items-center justify-center space-x-6 text-sm text-gray-600\">\n                  <div className=\"flex items-center space-x-1\">\n                    <ShieldCheckIcon className=\"h-4 w-4 text-green-500\" />\n                    <span>SSL Secured</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    <ShieldCheckIcon className=\"h-4 w-4 text-blue-500\" />\n                    <span>PayPal Protected</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    <ShieldCheckIcon className=\"h-4 w-4 text-purple-500\" />\n                    <span>GDPR Compliant</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Money Back Guarantee */}\n        <div className=\"mt-12 text-center bg-white rounded-2xl shadow-lg p-8\">\n          <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n            30-Day Money Back Guarantee\n          </h3>\n          <p className=\"text-gray-600 max-w-2xl mx-auto\">\n            We're confident you'll love our verification services. If you're not completely satisfied \n            with your results, we'll provide a full refund within 30 days of purchase.\n          </p>\n        </div>\n      </div>\n\n      {/* Payment Modal */}\n      <PaymentModal\n        isOpen={isModalOpen}\n        onClose={() => setIsModalOpen(false)}\n        selectedPlan={{\n          name: currentPlan.name,\n          basePrice: currentPlan.basePrice,\n          description: currentPlan.description,\n          unit: currentPlan.unit\n        }}\n        onPaymentSuccess={handlePaymentSuccess}\n      />\n    </div>\n  );\n};\n\nexport default CheckoutPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAOA,MAAM,eAAe;;IACnB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,QAAQ;QACZ,OAAO;YACL,MAAM;YACN,OAAO;YACP,WAAW;YACX,MAAM;YACN,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA,MAAM;YACJ,MAAM;YACN,OAAO;YACP,WAAW;YACX,MAAM;YACN,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA,WAAW;YACT,MAAM;YACN,OAAO;YACP,WAAW;YACX,MAAM;YACN,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,OAAO,aAAa,GAAG,CAAC;YAC9B,IAAI,QAAQ,KAAK,CAAC,KAA2B,EAAE;gBAC7C,gBAAgB;YAClB;QACF;iCAAG;QAAC;KAAa;IAEjB,MAAM,cAAc,KAAK,CAAC,aAAmC;IAE7D,MAAM,uBAAuB,CAAC;QAC5B,QAAQ,GAAG,CAAC,uBAAuB;QACnC,gBAAgB;QAEhB,2CAA2C;QAC3C,WAAW;YACT,gBAAgB;YAChB,6CAA6C;YAC7C,OAAO,IAAI,CAAC;QACd,GAAG;IACL;IAEA,MAAM,mBAAmB;QACvB,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,8NAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;;;;;;0CAE7B,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,OAAO,GAAG,CAAC;gDAAC,CAAC,KAAK,KAAK;iEACrC,6LAAC;gDAEC,WAAW,AAAC,yEAIX,OAHC,iBAAiB,MACb,+BACA;gDAEN,SAAS,IAAM,gBAAgB;0DAE/B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAuC,KAAK,IAAI;;;;;;8EAC9D,6LAAC;oEAAE,WAAU;8EAAiB,KAAK,WAAW;;;;;;;;;;;;sEAEhD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAmC;wEAC9C,KAAK,KAAK;wEACX,QAAQ,6BACP,6LAAC;4EAAK,WAAU;sFAAoC;;;;;;;;;;;;8EAGxD,6LAAC;oEAAI,WAAW,AAAC,sCAIhB,OAHC,iBAAiB,MACb,gCACA;8EAEH,iBAAiB,qBAChB,6LAAC,kNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CA1BxB;;;;;;;;;;;kDAoCX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDACX,YAAY,IAAI;oDAAC;;;;;;;0DAEpB,6LAAC;gDAAG,WAAU;0DACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAClC,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC,kNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEAAK,WAAU;0EAAiB;;;;;;;uDAF1B;;;;;;;;;;;;;;;;;;;;;;0CAUjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAiB,YAAY,IAAI;;;;;;kEACjD,6LAAC;wDAAK,WAAU;;4DAAgB;4DAAE,YAAY,KAAK;;;;;;;;;;;;;0DAErD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,6LAAC;gDAAG,WAAU;;;;;;0DACd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;;4DAAK;4DAAE,YAAY,KAAK;4DAAC;;;;;;;;;;;;;;;;;;;kDAK9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;4CAEnD,6BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;yGAGlC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;kEAGD,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEAGjD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAMZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8NAAA,CAAA,kBAAe;oEAAC,WAAU;;;;;;8EAC3B,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8NAAA,CAAA,kBAAe;oEAAC,WAAU;;;;;;8EAC3B,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8NAAA,CAAA,kBAAe;oEAAC,WAAU;;;;;;8EAC3B,6LAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAGrD,6LAAC;gCAAE,WAAU;0CAAkC;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC,qIAAA,CAAA,UAAY;gBACX,QAAQ;gBACR,SAAS,IAAM,eAAe;gBAC9B,cAAc;oBACZ,MAAM,YAAY,IAAI;oBACtB,WAAW,YAAY,SAAS;oBAChC,aAAa,YAAY,WAAW;oBACpC,MAAM,YAAY,IAAI;gBACxB;gBACA,kBAAkB;;;;;;;;;;;;AAI1B;GAjQM;;QACW,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAFhC;uCAmQS", "debugId": null}}, {"offset": {"line": 1263, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/solid/esm/CheckIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction CheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CheckIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,KAIlB,EAAE,MAAM;QAJU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJkB;IAKjB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1300, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/outline/esm/MinusIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MinusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M5 12h14\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MinusIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,KAIlB,EAAE,MAAM;QAJU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJkB;IAKjB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1339, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/outline/esm/PlusIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,KAIjB,EAAE,MAAM;QAJS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJiB;IAKhB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1380, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/next/src/client/set-attributes-from-props.ts"], "sourcesContent": ["const DOMAttributeNames: Record<string, string> = {\n  acceptCharset: 'accept-charset',\n  className: 'class',\n  htmlFor: 'for',\n  httpEquiv: 'http-equiv',\n  noModule: 'noModule',\n}\n\nconst ignoreProps = [\n  'onLoad',\n  'onReady',\n  'dangerouslySetInnerHTML',\n  'children',\n  'onError',\n  'strategy',\n  'stylesheets',\n]\n\nfunction isBooleanScriptAttribute(\n  attr: string\n): attr is 'async' | 'defer' | 'noModule' {\n  return ['async', 'defer', 'noModule'].includes(attr)\n}\n\nexport function setAttributesFromProps(el: HTMLElement, props: object) {\n  for (const [p, value] of Object.entries(props)) {\n    if (!props.hasOwnProperty(p)) continue\n    if (ignoreProps.includes(p)) continue\n\n    // we don't render undefined props to the DOM\n    if (value === undefined) {\n      continue\n    }\n\n    const attr = DOMAttributeNames[p] || p.toLowerCase()\n\n    if (el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr)) {\n      // Correctly assign boolean script attributes\n      // https://github.com/vercel/next.js/pull/20748\n      ;(el as HTMLScriptElement)[attr] = !!value\n    } else {\n      el.setAttribute(attr, String(value))\n    }\n\n    // Remove falsy non-zero boolean attributes so they are correctly interpreted\n    // (e.g. if we set them to false, this coerces to the string \"false\", which the browser interprets as true)\n    if (\n      value === false ||\n      (el.tagName === 'SCRIPT' &&\n        isBooleanScriptAttribute(attr) &&\n        (!value || value === 'false'))\n    ) {\n      // Call setAttribute before, as we need to set and unset the attribute to override force async:\n      // https://html.spec.whatwg.org/multipage/scripting.html#script-force-async\n      el.setAttribute(attr, '')\n      el.removeAttribute(attr)\n    }\n  }\n}\n"], "names": ["setAttributesFromProps", "DOMAttributeNames", "acceptCharset", "className", "htmlFor", "httpEquiv", "noModule", "ignoreProps", "isBooleanScriptAttribute", "attr", "includes", "el", "props", "p", "value", "Object", "entries", "hasOwnProperty", "undefined", "toLowerCase", "tagName", "setAttribute", "String", "removeAttribute"], "mappings": ";;;+BAwBgBA,0BAAAA;;;eAAAA;;;AAxBhB,MAAMC,oBAA4C;IAChDC,eAAe;IACfC,WAAW;IACXC,SAAS;IACTC,WAAW;IACXC,UAAU;AACZ;AAEA,MAAMC,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,yBACPC,IAAY;IAEZ,OAAO;QAAC;QAAS;QAAS;KAAW,CAACC,QAAQ,CAACD;AACjD;AAEO,SAAST,uBAAuBW,EAAe,EAAEC,KAAa;IACnE,KAAK,MAAM,CAACC,GAAGC,MAAM,IAAIC,OAAOC,OAAO,CAACJ,OAAQ;QAC9C,IAAI,CAACA,MAAMK,cAAc,CAACJ,IAAI;QAC9B,IAAIN,YAAYG,QAAQ,CAACG,IAAI;QAE7B,6CAA6C;QAC7C,IAAIC,UAAUI,WAAW;YACvB;QACF;QAEA,MAAMT,OAAOR,iBAAiB,CAACY,EAAE,IAAIA,EAAEM,WAAW;QAElD,IAAIR,GAAGS,OAAO,KAAK,YAAYZ,yBAAyBC,OAAO;YAC7D,6CAA6C;YAC7C,+CAA+C;;YAC7CE,EAAwB,CAACF,KAAK,GAAG,CAAC,CAACK;QACvC,OAAO;YACLH,GAAGU,YAAY,CAACZ,MAAMa,OAAOR;QAC/B;QAEA,6EAA6E;QAC7E,2GAA2G;QAC3G,IACEA,UAAU,SACTH,GAAGS,OAAO,KAAK,YACdZ,yBAAyBC,SACxB,CAAA,CAACK,SAASA,UAAU,OAAM,GAC7B;YACA,+FAA+F;YAC/F,2EAA2E;YAC3EH,GAAGU,YAAY,CAACZ,MAAM;YACtBE,GAAGY,eAAe,CAACd;QACrB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1452, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/next/src/client/request-idle-callback.ts"], "sourcesContent": ["export const requestIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.requestIdleCallback &&\n    self.requestIdleCallback.bind(window)) ||\n  function (cb: IdleRequestCallback): number {\n    let start = Date.now()\n    return self.setTimeout(function () {\n      cb({\n        didTimeout: false,\n        timeRemaining: function () {\n          return Math.max(0, 50 - (Date.now() - start))\n        },\n      })\n    }, 1)\n  }\n\nexport const cancelIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.cancelIdleCallback &&\n    self.cancelIdleCallback.bind(window)) ||\n  function (id: number) {\n    return clearTimeout(id)\n  }\n"], "names": ["cancelIdleCallback", "requestIdleCallback", "self", "bind", "window", "cb", "start", "Date", "now", "setTimeout", "didTimeout", "timeRemaining", "Math", "max", "id", "clearTimeout"], "mappings": ";;;;;;;;;;;;;;IAgBaA,kBAAkB,EAAA;eAAlBA;;IAhBAC,mBAAmB,EAAA;eAAnBA;;;AAAN,MAAMA,sBACV,OAAOC,SAAS,eACfA,KAAKD,mBAAmB,IACxBC,KAAKD,mBAAmB,CAACE,IAAI,CAACC,WAChC,SAAUC,EAAuB;IAC/B,IAAIC,QAAQC,KAAKC,GAAG;IACpB,OAAON,KAAKO,UAAU,CAAC;QACrBJ,GAAG;YACDK,YAAY;YACZC,eAAe;gBACb,OAAOC,KAAKC,GAAG,CAAC,GAAG,KAAMN,CAAAA,KAAKC,GAAG,KAAKF,KAAI;YAC5C;QACF;IACF,GAAG;AACL;AAEK,MAAMN,qBACV,OAAOE,SAAS,eACfA,KAAKF,kBAAkB,IACvBE,KAAKF,kBAAkB,CAACG,IAAI,CAACC,WAC/B,SAAUU,EAAU;IAClB,OAAOC,aAAaD;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/next/src/client/script.tsx"], "sourcesContent": ["'use client'\n\nimport ReactDOM from 'react-dom'\nimport React, { useEffect, useContext, useRef, type JSX } from 'react'\nimport type { ScriptHTMLAttributes } from 'react'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport { setAttributesFromProps } from './set-attributes-from-props'\nimport { requestIdleCallback } from './request-idle-callback'\n\nconst ScriptCache = new Map()\nconst LoadCache = new Set()\n\nexport interface ScriptProps extends ScriptHTMLAttributes<HTMLScriptElement> {\n  strategy?: 'afterInteractive' | 'lazyOnload' | 'beforeInteractive' | 'worker'\n  id?: string\n  onLoad?: (e: any) => void\n  onReady?: () => void | null\n  onError?: (e: any) => void\n  children?: React.ReactNode\n  stylesheets?: string[]\n}\n\n/**\n * @deprecated Use `ScriptProps` instead.\n */\nexport type Props = ScriptProps\n\nconst insertStylesheets = (stylesheets: string[]) => {\n  // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad\n  //\n  // Using ReactDOM.preinit to feature detect appDir and inject styles\n  // Stylesheets might have already been loaded if initialized with Script component\n  // Re-inject styles here to handle scripts loaded via handleClientScriptLoad\n  // ReactDOM.preinit handles dedup and ensures the styles are loaded only once\n  if (ReactDOM.preinit) {\n    stylesheets.forEach((stylesheet: string) => {\n      ReactDOM.preinit(stylesheet, { as: 'style' })\n    })\n\n    return\n  }\n\n  // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad\n  //\n  // We use this function to load styles when appdir is not detected\n  // TODO: Use React float APIs to load styles once available for pages dir\n  if (typeof window !== 'undefined') {\n    let head = document.head\n    stylesheets.forEach((stylesheet: string) => {\n      let link = document.createElement('link')\n\n      link.type = 'text/css'\n      link.rel = 'stylesheet'\n      link.href = stylesheet\n\n      head.appendChild(link)\n    })\n  }\n}\n\nconst loadScript = (props: ScriptProps): void => {\n  const {\n    src,\n    id,\n    onLoad = () => {},\n    onReady = null,\n    dangerouslySetInnerHTML,\n    children = '',\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n  } = props\n\n  const cacheKey = id || src\n\n  // Script has already loaded\n  if (cacheKey && LoadCache.has(cacheKey)) {\n    return\n  }\n\n  // Contents of this script are already loading/loaded\n  if (ScriptCache.has(src)) {\n    LoadCache.add(cacheKey)\n    // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n    // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n    ScriptCache.get(src).then(onLoad, onError)\n    return\n  }\n\n  /** Execute after the script first loaded */\n  const afterLoad = () => {\n    // Run onReady for the first time after load event\n    if (onReady) {\n      onReady()\n    }\n    // add cacheKey to LoadCache when load successfully\n    LoadCache.add(cacheKey)\n  }\n\n  const el = document.createElement('script')\n\n  const loadPromise = new Promise<void>((resolve, reject) => {\n    el.addEventListener('load', function (e) {\n      resolve()\n      if (onLoad) {\n        onLoad.call(this, e)\n      }\n      afterLoad()\n    })\n    el.addEventListener('error', function (e) {\n      reject(e)\n    })\n  }).catch(function (e) {\n    if (onError) {\n      onError(e)\n    }\n  })\n\n  if (dangerouslySetInnerHTML) {\n    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n    el.innerHTML = (dangerouslySetInnerHTML.__html as string) || ''\n\n    afterLoad()\n  } else if (children) {\n    el.textContent =\n      typeof children === 'string'\n        ? children\n        : Array.isArray(children)\n          ? children.join('')\n          : ''\n\n    afterLoad()\n  } else if (src) {\n    el.src = src\n    // do not add cacheKey into LoadCache for remote script here\n    // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n\n    ScriptCache.set(src, loadPromise)\n  }\n\n  setAttributesFromProps(el, props)\n\n  if (strategy === 'worker') {\n    el.setAttribute('type', 'text/partytown')\n  }\n\n  el.setAttribute('data-nscript', strategy)\n\n  // Load styles associated with this script\n  if (stylesheets) {\n    insertStylesheets(stylesheets)\n  }\n\n  document.body.appendChild(el)\n}\n\nexport function handleClientScriptLoad(props: ScriptProps) {\n  const { strategy = 'afterInteractive' } = props\n  if (strategy === 'lazyOnload') {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  } else {\n    loadScript(props)\n  }\n}\n\nfunction loadLazyScript(props: ScriptProps) {\n  if (document.readyState === 'complete') {\n    requestIdleCallback(() => loadScript(props))\n  } else {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  }\n}\n\nfunction addBeforeInteractiveToCache() {\n  const scripts = [\n    ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n    ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]'),\n  ]\n  scripts.forEach((script) => {\n    const cacheKey = script.id || script.getAttribute('src')\n    LoadCache.add(cacheKey)\n  })\n}\n\nexport function initScriptLoader(scriptLoaderItems: ScriptProps[]) {\n  scriptLoaderItems.forEach(handleClientScriptLoad)\n  addBeforeInteractiveToCache()\n}\n\n/**\n * Load a third-party scripts in an optimized way.\n *\n * Read more: [Next.js Docs: `next/script`](https://nextjs.org/docs/app/api-reference/components/script)\n */\nfunction Script(props: ScriptProps): JSX.Element | null {\n  const {\n    id,\n    src = '',\n    onLoad = () => {},\n    onReady = null,\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n    ...restProps\n  } = props\n\n  // Context is available only during SSR\n  let { updateScripts, scripts, getIsSsr, appDir, nonce } =\n    useContext(HeadManagerContext)\n\n  // if a nonce is explicitly passed to the script tag, favor that over the automatic handling\n  nonce = restProps.nonce || nonce\n\n  /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */\n  const hasOnReadyEffectCalled = useRef(false)\n\n  useEffect(() => {\n    const cacheKey = id || src\n    if (!hasOnReadyEffectCalled.current) {\n      // Run onReady if script has loaded before but component is re-mounted\n      if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n        onReady()\n      }\n\n      hasOnReadyEffectCalled.current = true\n    }\n  }, [onReady, id, src])\n\n  const hasLoadScriptEffectCalled = useRef(false)\n\n  useEffect(() => {\n    if (!hasLoadScriptEffectCalled.current) {\n      if (strategy === 'afterInteractive') {\n        loadScript(props)\n      } else if (strategy === 'lazyOnload') {\n        loadLazyScript(props)\n      }\n\n      hasLoadScriptEffectCalled.current = true\n    }\n  }, [props, strategy])\n\n  if (strategy === 'beforeInteractive' || strategy === 'worker') {\n    if (updateScripts) {\n      scripts[strategy] = (scripts[strategy] || []).concat([\n        {\n          id,\n          src,\n          onLoad,\n          onReady,\n          onError,\n          ...restProps,\n          nonce,\n        },\n      ])\n      updateScripts(scripts)\n    } else if (getIsSsr && getIsSsr()) {\n      // Script has already loaded during SSR\n      LoadCache.add(id || src)\n    } else if (getIsSsr && !getIsSsr()) {\n      loadScript({\n        ...props,\n        nonce,\n      })\n    }\n  }\n\n  // For the app directory, we need React Float to preload these scripts.\n  if (appDir) {\n    // Injecting stylesheets here handles beforeInteractive and worker scripts correctly\n    // For other strategies injecting here ensures correct stylesheet order\n    // ReactDOM.preinit handles loading the styles in the correct order,\n    // also ensures the stylesheet is loaded only once and in a consistent manner\n    //\n    // Case 1: Styles for beforeInteractive/worker with appDir - handled here\n    // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet\n    // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here\n    // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function\n    if (stylesheets) {\n      stylesheets.forEach((styleSrc) => {\n        ReactDOM.preinit(styleSrc, { as: 'style' })\n      })\n    }\n\n    // Before interactive scripts need to be loaded by Next.js' runtime instead\n    // of native <script> tags, because they no longer have `defer`.\n    if (strategy === 'beforeInteractive') {\n      if (!src) {\n        // For inlined scripts, we put the content in `children`.\n        if (restProps.dangerouslySetInnerHTML) {\n          // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n          restProps.children = restProps.dangerouslySetInnerHTML\n            .__html as string\n          delete restProps.dangerouslySetInnerHTML\n        }\n\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                0,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      } else {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                src,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      }\n    } else if (strategy === 'afterInteractive') {\n      if (src) {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n      }\n    }\n  }\n\n  return null\n}\n\nObject.defineProperty(Script, '__nextScript', { value: true })\n\nexport default Script\n"], "names": ["handleClientScriptLoad", "initScriptLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "Load<PERSON>ache", "Set", "insertStylesheets", "stylesheets", "ReactDOM", "preinit", "for<PERSON>ach", "stylesheet", "as", "window", "head", "document", "link", "createElement", "type", "rel", "href", "append<PERSON><PERSON><PERSON>", "loadScript", "props", "src", "id", "onLoad", "onReady", "dangerouslySetInnerHTML", "children", "strategy", "onError", "cache<PERSON>ey", "has", "add", "get", "then", "afterLoad", "el", "loadPromise", "Promise", "resolve", "reject", "addEventListener", "e", "call", "catch", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "set", "setAttributesFromProps", "setAttribute", "body", "requestIdleCallback", "loadLazyScript", "readyState", "addBeforeInteractiveToCache", "scripts", "querySelectorAll", "script", "getAttribute", "scriptLoaderItems", "<PERSON><PERSON><PERSON>", "restProps", "updateScripts", "getIsSsr", "appDir", "nonce", "useContext", "HeadManagerContext", "hasOnReadyEffectCalled", "useRef", "useEffect", "current", "hasLoadScriptEffectCalled", "concat", "styleSrc", "JSON", "stringify", "preload", "integrity", "crossOrigin", "Object", "defineProperty", "value"], "mappings": ";;;;;;;;;;;;;;;IAgYA,OAAqB,EAAA;eAArB;;IApOgBA,sBAAsB,EAAA;eAAtBA;;IAgCAC,gBAAgB,EAAA;eAAhBA;;;;;;mEA1LK;iEAC0C;iDAE5B;wCACI;qCACH;AAEpC,MAAMC,cAAc,IAAIC;AACxB,MAAMC,YAAY,IAAIC;AAiBtB,MAAMC,oBAAoB,CAACC;IACzB,iGAAiG;IACjG,EAAE;IACF,oEAAoE;IACpE,kFAAkF;IAClF,4EAA4E;IAC5E,6EAA6E;IAC7E,IAAIC,UAAAA,OAAQ,CAACC,OAAO,EAAE;QACpBF,YAAYG,OAAO,CAAC,CAACC;YACnBH,UAAAA,OAAQ,CAACC,OAAO,CAACE,YAAY;gBAAEC,IAAI;YAAQ;QAC7C;QAEA;IACF;IAEA,gGAAgG;IAChG,EAAE;IACF,kEAAkE;IAClE,yEAAyE;IACzE,IAAI,OAAOC,WAAW,aAAa;QACjC,IAAIC,OAAOC,SAASD,IAAI;QACxBP,YAAYG,OAAO,CAAC,CAACC;YACnB,IAAIK,OAAOD,SAASE,aAAa,CAAC;YAElCD,KAAKE,IAAI,GAAG;YACZF,KAAKG,GAAG,GAAG;YACXH,KAAKI,IAAI,GAAGT;YAEZG,KAAKO,WAAW,CAACL;QACnB;IACF;AACF;AAEA,MAAMM,aAAa,CAACC;IAClB,MAAM,EACJC,GAAG,EACHC,EAAE,EACFC,SAAS,KAAO,CAAC,EACjBC,UAAU,IAAI,EACdC,uBAAuB,EACvBC,WAAW,EAAE,EACbC,WAAW,kBAAkB,EAC7BC,OAAO,EACPxB,WAAW,EACZ,GAAGgB;IAEJ,MAAMS,WAAWP,MAAMD;IAEvB,4BAA4B;IAC5B,IAAIQ,YAAY5B,UAAU6B,GAAG,CAACD,WAAW;QACvC;IACF;IAEA,qDAAqD;IACrD,IAAI9B,YAAY+B,GAAG,CAACT,MAAM;QACxBpB,UAAU8B,GAAG,CAACF;QACd,wGAAwG;QACxG,sGAAsG;QACtG9B,YAAYiC,GAAG,CAACX,KAAKY,IAAI,CAACV,QAAQK;QAClC;IACF;IAEA,0CAA0C,GAC1C,MAAMM,YAAY;QAChB,kDAAkD;QAClD,IAAIV,SAAS;YACXA;QACF;QACA,mDAAmD;QACnDvB,UAAU8B,GAAG,CAACF;IAChB;IAEA,MAAMM,KAAKvB,SAASE,aAAa,CAAC;IAElC,MAAMsB,cAAc,IAAIC,QAAc,CAACC,SAASC;QAC9CJ,GAAGK,gBAAgB,CAAC,QAAQ,SAAUC,CAAC;YACrCH;YACA,IAAIf,QAAQ;gBACVA,OAAOmB,IAAI,CAAC,IAAI,EAAED;YACpB;YACAP;QACF;QACAC,GAAGK,gBAAgB,CAAC,SAAS,SAAUC,CAAC;YACtCF,OAAOE;QACT;IACF,GAAGE,KAAK,CAAC,SAAUF,CAAC;QAClB,IAAIb,SAAS;YACXA,QAAQa;QACV;IACF;IAEA,IAAIhB,yBAAyB;QAC3B,2DAA2D;QAC3DU,GAAGS,SAAS,GAAInB,wBAAwBoB,MAAM,IAAe;QAE7DX;IACF,OAAO,IAAIR,UAAU;QACnBS,GAAGW,WAAW,GACZ,OAAOpB,aAAa,WAChBA,WACAqB,MAAMC,OAAO,CAACtB,YACZA,SAASuB,IAAI,CAAC,MACd;QAERf;IACF,OAAO,IAAIb,KAAK;QACdc,GAAGd,GAAG,GAAGA;QACT,4DAA4D;QAC5D,yFAAyF;QAEzFtB,YAAYmD,GAAG,CAAC7B,KAAKe;IACvB;IAEAe,CAAAA,GAAAA,wBAAAA,sBAAsB,EAAChB,IAAIf;IAE3B,IAAIO,aAAa,UAAU;QACzBQ,GAAGiB,YAAY,CAAC,QAAQ;IAC1B;IAEAjB,GAAGiB,YAAY,CAAC,gBAAgBzB;IAEhC,0CAA0C;IAC1C,IAAIvB,aAAa;QACfD,kBAAkBC;IACpB;IAEAQ,SAASyC,IAAI,CAACnC,WAAW,CAACiB;AAC5B;AAEO,SAAStC,uBAAuBuB,KAAkB;IACvD,MAAM,EAAEO,WAAW,kBAAkB,EAAE,GAAGP;IAC1C,IAAIO,aAAa,cAAc;QAC7BjB,OAAO8B,gBAAgB,CAAC,QAAQ;YAC9Bc,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAMnC,WAAWC;QACvC;IACF,OAAO;QACLD,WAAWC;IACb;AACF;AAEA,SAASmC,eAAenC,KAAkB;IACxC,IAAIR,SAAS4C,UAAU,KAAK,YAAY;QACtCF,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAMnC,WAAWC;IACvC,OAAO;QACLV,OAAO8B,gBAAgB,CAAC,QAAQ;YAC9Bc,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAMnC,WAAWC;QACvC;IACF;AACF;AAEA,SAASqC;IACP,MAAMC,UAAU;WACX9C,SAAS+C,gBAAgB,CAAC;WAC1B/C,SAAS+C,gBAAgB,CAAC;KAC9B;IACDD,QAAQnD,OAAO,CAAC,CAACqD;QACf,MAAM/B,WAAW+B,OAAOtC,EAAE,IAAIsC,OAAOC,YAAY,CAAC;QAClD5D,UAAU8B,GAAG,CAACF;IAChB;AACF;AAEO,SAAS/B,iBAAiBgE,iBAAgC;IAC/DA,kBAAkBvD,OAAO,CAACV;IAC1B4D;AACF;AAEA;;;;CAIC,GACD,SAASM,OAAO3C,KAAkB;IAChC,MAAM,EACJE,EAAE,EACFD,MAAM,EAAE,EACRE,SAAS,KAAO,CAAC,EACjBC,UAAU,IAAI,EACdG,WAAW,kBAAkB,EAC7BC,OAAO,EACPxB,WAAW,EACX,GAAG4D,WACJ,GAAG5C;IAEJ,uCAAuC;IACvC,IAAI,EAAE6C,aAAa,EAAEP,OAAO,EAAEQ,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAE,GACrDC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,iCAAAA,kBAAkB;IAE/B,4FAA4F;IAC5FF,QAAQJ,UAAUI,KAAK,IAAIA;IAE3B;;;;;;;;;;;;;;;;;;;;;;;;;GAyBC,GACD,MAAMG,yBAAyBC,CAAAA,GAAAA,OAAAA,MAAM,EAAC;IAEtCC,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACR,MAAM5C,WAAWP,MAAMD;QACvB,IAAI,CAACkD,uBAAuBG,OAAO,EAAE;YACnC,sEAAsE;YACtE,IAAIlD,WAAWK,YAAY5B,UAAU6B,GAAG,CAACD,WAAW;gBAClDL;YACF;YAEA+C,uBAAuBG,OAAO,GAAG;QACnC;IACF,GAAG;QAAClD;QAASF;QAAID;KAAI;IAErB,MAAMsD,4BAA4BH,CAAAA,GAAAA,OAAAA,MAAM,EAAC;IAEzCC,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACR,IAAI,CAACE,0BAA0BD,OAAO,EAAE;YACtC,IAAI/C,aAAa,oBAAoB;gBACnCR,WAAWC;YACb,OAAO,IAAIO,aAAa,cAAc;gBACpC4B,eAAenC;YACjB;YAEAuD,0BAA0BD,OAAO,GAAG;QACtC;IACF,GAAG;QAACtD;QAAOO;KAAS;IAEpB,IAAIA,aAAa,uBAAuBA,aAAa,UAAU;QAC7D,IAAIsC,eAAe;YACjBP,OAAO,CAAC/B,SAAS,GAAI+B,CAAAA,OAAO,CAAC/B,SAAS,IAAI,EAAC,EAAGiD,MAAM,CAAC;gBACnD;oBACEtD;oBACAD;oBACAE;oBACAC;oBACAI;oBACA,GAAGoC,SAAS;oBACZI;gBACF;aACD;YACDH,cAAcP;QAChB,OAAO,IAAIQ,YAAYA,YAAY;YACjC,uCAAuC;YACvCjE,UAAU8B,GAAG,CAACT,MAAMD;QACtB,OAAO,IAAI6C,YAAY,CAACA,YAAY;YAClC/C,WAAW;gBACT,GAAGC,KAAK;gBACRgD;YACF;QACF;IACF;IAEA,uEAAuE;IACvE,IAAID,QAAQ;QACV,oFAAoF;QACpF,uEAAuE;QACvE,oEAAoE;QACpE,6EAA6E;QAC7E,EAAE;QACF,yEAAyE;QACzE,+EAA+E;QAC/E,4EAA4E;QAC5E,wGAAwG;QACxG,IAAI/D,aAAa;YACfA,YAAYG,OAAO,CAAC,CAACsE;gBACnBxE,UAAAA,OAAQ,CAACC,OAAO,CAACuE,UAAU;oBAAEpE,IAAI;gBAAQ;YAC3C;QACF;QAEA,2EAA2E;QAC3E,gEAAgE;QAChE,IAAIkB,aAAa,qBAAqB;YACpC,IAAI,CAACN,KAAK;gBACR,yDAAyD;gBACzD,IAAI2C,UAAUvC,uBAAuB,EAAE;oBACrC,2DAA2D;oBAC3DuC,UAAUtC,QAAQ,GAAGsC,UAAUvC,uBAAuB,CACnDoB,MAAM;oBACT,OAAOmB,UAAUvC,uBAAuB;gBAC1C;gBAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACmC,UAAAA;oBACCQ,OAAOA;oBACP3C,yBAAyB;wBACvBoB,QAAS,4CAAyCiC,KAAKC,SAAS,CAAC;4BAC/D;4BACA;gCAAE,GAAGf,SAAS;gCAAE1C;4BAAG;yBACpB,IAAE;oBACL;;YAGN,OAAO;gBACL,aAAa;gBACbjB,UAAAA,OAAQ,CAAC2E,OAAO,CACd3D,KACA2C,UAAUiB,SAAS,GACf;oBACExE,IAAI;oBACJwE,WAAWjB,UAAUiB,SAAS;oBAC9Bb;oBACAc,aAAalB,UAAUkB,WAAW;gBACpC,IACA;oBAAEzE,IAAI;oBAAU2D;oBAAOc,aAAalB,UAAUkB,WAAW;gBAAC;gBAEhE,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACtB,UAAAA;oBACCQ,OAAOA;oBACP3C,yBAAyB;wBACvBoB,QAAS,4CAAyCiC,KAAKC,SAAS,CAAC;4BAC/D1D;4BACA;gCAAE,GAAG2C,SAAS;gCAAE1C;4BAAG;yBACpB,IAAE;oBACL;;YAGN;QACF,OAAO,IAAIK,aAAa,oBAAoB;YAC1C,IAAIN,KAAK;gBACP,aAAa;gBACbhB,UAAAA,OAAQ,CAAC2E,OAAO,CACd3D,KACA2C,UAAUiB,SAAS,GACf;oBACExE,IAAI;oBACJwE,WAAWjB,UAAUiB,SAAS;oBAC9Bb;oBACAc,aAAalB,UAAUkB,WAAW;gBACpC,IACA;oBAAEzE,IAAI;oBAAU2D;oBAAOc,aAAalB,UAAUkB,WAAW;gBAAC;YAElE;QACF;IACF;IAEA,OAAO;AACT;AAEAC,OAAOC,cAAc,CAACrB,QAAQ,gBAAgB;IAAEsB,OAAO;AAAK;MAE5D,WAAetB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1849, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/next/script.js"], "sourcesContent": ["module.exports = require('./dist/client/script')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}