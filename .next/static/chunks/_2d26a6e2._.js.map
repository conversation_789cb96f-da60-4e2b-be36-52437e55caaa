{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  ShieldCheckIcon, \n  ClockIcon, \n  XCircleIcon,\n  CheckCircleIcon,\n  UserIcon,\n  DocumentTextIcon,\n  CalendarIcon\n} from '@heroicons/react/24/solid';\n\nconst DashboardPage = () => {\n  const [formData, setFormData] = useState({\n    fullName: '',\n    dateOfBirth: '',\n    nationalId: '',\n    verificationType: 'basic'\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // Mock verification history data\n  const verificationHistory = [\n    {\n      id: 'VER-001',\n      name: '<PERSON>',\n      type: 'Deep ID Scan',\n      status: 'Verified',\n      date: '2024-01-15',\n      amount: '$24.99'\n    },\n    {\n      id: 'VER-002',\n      name: '<PERSON>',\n      type: 'Basic Check',\n      status: 'Pending',\n      date: '2024-01-14',\n      amount: '$9.99'\n    },\n    {\n      id: 'VER-003',\n      name: '<PERSON>',\n      type: 'Deep ID Scan',\n      status: 'Failed',\n      date: '2024-01-13',\n      amount: '$24.99'\n    },\n    {\n      id: 'VER-004',\n      name: '<PERSON>',\n      type: 'Basic Check',\n      status: 'Verified',\n      date: '2024-01-12',\n      amount: '$9.99'\n    }\n  ];\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate API call\n    setTimeout(() => {\n      setIsSubmitting(false);\n      // Reset form\n      setFormData({\n        fullName: '',\n        dateOfBirth: '',\n        nationalId: '',\n        verificationType: 'basic'\n      });\n      alert('Verification request submitted successfully!');\n    }, 2000);\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'Verified':\n        return <CheckCircleIcon className=\"h-5 w-5 text-green-500\" />;\n      case 'Pending':\n        return <ClockIcon className=\"h-5 w-5 text-yellow-500\" />;\n      case 'Failed':\n        return <XCircleIcon className=\"h-5 w-5 text-red-500\" />;\n      default:\n        return <ClockIcon className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'Verified':\n        return 'bg-green-100 text-green-800';\n      case 'Pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'Failed':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\n          <p className=\"text-gray-600\">Manage your verification requests and view your history</p>\n        </div>\n\n        {/* Summary Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white p-6 rounded-2xl shadow-md\">\n            <div className=\"flex items-center\">\n              <div className=\"bg-blue-100 p-3 rounded-full\">\n                <ShieldCheckIcon className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Verifications</p>\n                <p className=\"text-2xl font-bold text-gray-900\">24</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-2xl shadow-md\">\n            <div className=\"flex items-center\">\n              <div className=\"bg-green-100 p-3 rounded-full\">\n                <CheckCircleIcon className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Verified</p>\n                <p className=\"text-2xl font-bold text-gray-900\">18</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-2xl shadow-md\">\n            <div className=\"flex items-center\">\n              <div className=\"bg-yellow-100 p-3 rounded-full\">\n                <ClockIcon className=\"h-6 w-6 text-yellow-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Pending</p>\n                <p className=\"text-2xl font-bold text-gray-900\">4</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-2xl shadow-md\">\n            <div className=\"flex items-center\">\n              <div className=\"bg-red-100 p-3 rounded-full\">\n                <XCircleIcon className=\"h-6 w-6 text-red-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Failed</p>\n                <p className=\"text-2xl font-bold text-gray-900\">2</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Verification Form */}\n          <div className=\"bg-white p-8 rounded-2xl shadow-md\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">New Verification Request</h2>\n            \n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div>\n                <label htmlFor=\"fullName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  <UserIcon className=\"h-4 w-4 inline mr-1\" />\n                  Full Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"fullName\"\n                  name=\"fullName\"\n                  value={formData.fullName}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Enter full name\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"dateOfBirth\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  <CalendarIcon className=\"h-4 w-4 inline mr-1\" />\n                  Date of Birth\n                </label>\n                <input\n                  type=\"date\"\n                  id=\"dateOfBirth\"\n                  name=\"dateOfBirth\"\n                  value={formData.dateOfBirth}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"nationalId\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  <DocumentTextIcon className=\"h-4 w-4 inline mr-1\" />\n                  National ID / SSN\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"nationalId\"\n                  name=\"nationalId\"\n                  value={formData.nationalId}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Enter National ID or SSN\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"verificationType\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Verification Type\n                </label>\n                <select\n                  id=\"verificationType\"\n                  name=\"verificationType\"\n                  value={formData.verificationType}\n                  onChange={handleInputChange}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"basic\">Basic Check - $9.99</option>\n                  <option value=\"deep\">Deep ID Scan - $24.99</option>\n                  <option value=\"unlimited\">Monthly Unlimited - $49.99</option>\n                </select>\n              </div>\n\n              <button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-xl font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\"\n              >\n                {isSubmitting ? (\n                  <div className=\"flex items-center justify-center\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Processing...\n                  </div>\n                ) : (\n                  'Run Check'\n                )}\n              </button>\n            </form>\n          </div>\n\n          {/* Verification History */}\n          <div className=\"bg-white p-8 rounded-2xl shadow-md\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Recent Verifications</h2>\n            \n            <div className=\"space-y-4\">\n              {verificationHistory.map((verification) => (\n                <div key={verification.id} className=\"border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow duration-200\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      {getStatusIcon(verification.status)}\n                      <span className=\"font-medium text-gray-900\">{verification.name}</span>\n                    </div>\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(verification.status)}`}>\n                      {verification.status}\n                    </span>\n                  </div>\n                  <div className=\"text-sm text-gray-600\">\n                    <p>ID: {verification.id}</p>\n                    <p>Type: {verification.type}</p>\n                    <div className=\"flex justify-between mt-2\">\n                      <span>{verification.date}</span>\n                      <span className=\"font-medium\">{verification.amount}</span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            <div className=\"mt-6 text-center\">\n              <button className=\"text-blue-600 hover:text-blue-700 font-medium\">\n                View All History\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DashboardPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAaA,MAAM,gBAAgB;;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,aAAa;QACb,YAAY;QACZ,kBAAkB;IACpB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,iCAAiC;IACjC,MAAM,sBAAsB;QAC1B;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA;YACE,IAAI;Y<PERSON><PERSON>,MAAM;YACN,MAAM;YACN,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;KACD;IAED,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,oBAAoB;QACpB,WAAW;YACT,gBAAgB;YAChB,aAAa;YACb,YAAY;gBACV,UAAU;gBACV,aAAa;gBACb,YAAY;gBACZ,kBAAkB;YACpB;YACA,MAAM;QACR,GAAG;IACL;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,6LAAC,kNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,sNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,kNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAI/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,8NAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;;;;;;kDAE7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;;;;;;;;;;;;sCAKtD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,8NAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;;;;;;kDAE7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;;;;;;;;;;;;sCAKtD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,kNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;;;;;;;;;;;;sCAKtD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,sNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMxD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAEtD,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAW,WAAU;;sEAClC,6LAAC,gNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAwB;;;;;;;8DAG9C,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAc,WAAU;;sEACrC,6LAAC,wNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAwB;;;;;;;8DAGlD,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU;oDACV,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAId,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAa,WAAU;;sEACpC,6LAAC,gOAAA,CAAA,mBAAgB;4DAAC,WAAU;;;;;;wDAAwB;;;;;;;8DAGtD,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,UAAU;oDAC1B,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAmB,WAAU;8DAA+C;;;;;;8DAG3F,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,gBAAgB;oDAChC,UAAU;oDACV,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAQ;;;;;;sEACtB,6LAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,6LAAC;4DAAO,OAAM;sEAAY;;;;;;;;;;;;;;;;;;sDAI9B,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,6BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;oDAAuE;;;;;;2FAIxF;;;;;;;;;;;;;;;;;;sCAOR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAEtD,6LAAC;oCAAI,WAAU;8CACZ,oBAAoB,GAAG,CAAC,CAAC,6BACxB,6LAAC;4CAA0B,WAAU;;8DACnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,cAAc,aAAa,MAAM;8EAClC,6LAAC;oEAAK,WAAU;8EAA6B,aAAa,IAAI;;;;;;;;;;;;sEAEhE,6LAAC;4DAAK,WAAW,AAAC,8CAAiF,OAApC,eAAe,aAAa,MAAM;sEAC9F,aAAa,MAAM;;;;;;;;;;;;8DAGxB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAE;gEAAK,aAAa,EAAE;;;;;;;sEACvB,6LAAC;;gEAAE;gEAAO,aAAa,IAAI;;;;;;;sEAC3B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAM,aAAa,IAAI;;;;;;8EACxB,6LAAC;oEAAK,WAAU;8EAAe,aAAa,MAAM;;;;;;;;;;;;;;;;;;;2CAf9C,aAAa,EAAE;;;;;;;;;;8CAsB7B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAO,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShF;GA7RM;KAAA;uCA+RS", "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/solid/esm/ClockIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 6a.75.75 0 0 0-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 0 0 0-1.5h-3.75V6Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,KAIlB,EAAE,MAAM;QAJU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJkB;IAKjB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/solid/esm/XCircleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction XCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XCircleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,YAAY,KAIpB,EAAE,MAAM;QAJY,EACnB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJoB;IAKnB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 867, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/solid/esm/CheckCircleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction CheckCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CheckCircleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,KAIxB,EAAE,MAAM;QAJgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJwB;IAKvB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 904, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/solid/esm/UserIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction UserIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UserIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,KAIjB,EAAE,MAAM;QAJS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJiB;IAKhB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/solid/esm/DocumentTextIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction DocumentTextIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625ZM7.5 15a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-7.5A.75.75 0 0 1 7.5 15Zm.75 2.25a.75.75 0 0 0 0 1.5H12a.75.75 0 0 0 0-1.5H8.25Z\",\n    clipRule: \"evenodd\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(DocumentTextIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,iBAAiB,KAIzB,EAAE,MAAM;QAJiB,EACxB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJyB;IAKxB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC3C,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 980, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/solid/esm/CalendarIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction CalendarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M6.75 2.25A.75.75 0 0 1 7.5 3v1.5h9V3A.75.75 0 0 1 18 3v1.5h.75a3 3 0 0 1 3 3v11.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V7.5a3 3 0 0 1 3-3H6V3a.75.75 0 0 1 .75-.75Zm13.5 9a1.5 1.5 0 0 0-1.5-1.5H5.25a1.5 1.5 0 0 0-1.5 1.5v7.5a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5v-7.5Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CalendarIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,KAIrB,EAAE,MAAM;QAJa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJqB;IAKpB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}