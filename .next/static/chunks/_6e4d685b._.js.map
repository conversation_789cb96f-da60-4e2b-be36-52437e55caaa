{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/src/app/checkout/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { PayPalScriptProvider, PayPalButtons } from '@paypal/react-paypal-js';\nimport { CheckIcon, ShieldCheckIcon } from '@heroicons/react/24/solid';\n\nconst CheckoutPage = () => {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const [selectedPlan, setSelectedPlan] = useState('basic');\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  const plans = {\n    basic: {\n      name: \"Basic Check\",\n      price: \"9.99\",\n      description: \"Perfect for individual verification needs\",\n      features: [\n        \"Identity verification\",\n        \"Basic background check\",\n        \"Email support\",\n        \"24-hour processing\",\n        \"Secure document upload\",\n        \"PDF report delivery\"\n      ]\n    },\n    deep: {\n      name: \"Deep ID Scan\",\n      price: \"24.99\",\n      description: \"Comprehensive verification for businesses\",\n      features: [\n        \"Everything in Basic Check\",\n        \"Advanced background screening\",\n        \"Criminal record check\",\n        \"Employment verification\",\n        \"Priority support\",\n        \"1-hour processing\",\n        \"API access\",\n        \"Bulk upload capability\"\n      ]\n    },\n    unlimited: {\n      name: \"Monthly Unlimited Pass\",\n      price: \"49.99\",\n      description: \"Unlimited verifications for growing businesses\",\n      features: [\n        \"Everything in Deep ID Scan\",\n        \"Unlimited verifications\",\n        \"Real-time processing\",\n        \"Dedicated account manager\",\n        \"Custom integrations\",\n        \"White-label reports\",\n        \"Advanced analytics\",\n        \"Phone support\",\n        \"SLA guarantee\"\n      ]\n    }\n  };\n\n  useEffect(() => {\n    const plan = searchParams.get('plan');\n    if (plan && plans[plan as keyof typeof plans]) {\n      setSelectedPlan(plan);\n    }\n  }, [searchParams]);\n\n  const currentPlan = plans[selectedPlan as keyof typeof plans];\n\n  const paypalOptions = {\n    clientId: \"test\", // Replace with your actual PayPal client ID\n    currency: \"USD\",\n    intent: \"capture\",\n    enableFunding: \"card\" as any,\n  };\n\n  const createOrder = (data: any, actions: any) => {\n    return actions.order.create({\n      purchase_units: [\n        {\n          amount: {\n            value: currentPlan.price,\n            currency_code: \"USD\"\n          },\n          description: `iVerifyPro - ${currentPlan.name}`\n        }\n      ]\n    });\n  };\n\n  const onApprove = async (data: any, actions: any) => {\n    setIsProcessing(true);\n    try {\n      const details = await actions.order.capture();\n      console.log('Payment successful:', details);\n      \n      // Simulate API call to process the payment\n      setTimeout(() => {\n        setIsProcessing(false);\n        // Redirect to dashboard with success message\n        router.push('/dashboard?payment=success');\n      }, 2000);\n    } catch (error) {\n      console.error('Payment error:', error);\n      setIsProcessing(false);\n      alert('Payment failed. Please try again.');\n    }\n  };\n\n  const onError = (err: any) => {\n    console.error('PayPal error:', err);\n    alert('Payment error occurred. Please try again.');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-12\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <div className=\"flex justify-center mb-4\">\n            <ShieldCheckIcon className=\"h-12 w-12 text-blue-600\" />\n          </div>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Complete Your Purchase</h1>\n          <p className=\"text-lg text-gray-600\">\n            Secure checkout powered by PayPal with guest payment options\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Plan Selection */}\n          <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Select Your Plan</h2>\n            \n            <div className=\"space-y-4\">\n              {Object.entries(plans).map(([key, plan]) => (\n                <div\n                  key={key}\n                  className={`border-2 rounded-xl p-4 cursor-pointer transition-colors duration-200 ${\n                    selectedPlan === key\n                      ? 'border-blue-600 bg-blue-50'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }`}\n                  onClick={() => setSelectedPlan(key)}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900\">{plan.name}</h3>\n                      <p className=\"text-gray-600\">{plan.description}</p>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-2xl font-bold text-gray-900\">\n                        ${plan.price}\n                        {key === 'unlimited' && (\n                          <span className=\"text-sm text-gray-600 font-normal\">/month</span>\n                        )}\n                      </div>\n                      <div className={`w-4 h-4 rounded-full border-2 mt-2 ${\n                        selectedPlan === key\n                          ? 'bg-blue-600 border-blue-600'\n                          : 'border-gray-300'\n                      }`}>\n                        {selectedPlan === key && (\n                          <CheckIcon className=\"h-3 w-3 text-white m-0.5\" />\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Selected Plan Features */}\n            <div className=\"mt-8 p-6 bg-gray-50 rounded-xl\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                {currentPlan.name} includes:\n              </h3>\n              <ul className=\"space-y-2\">\n                {currentPlan.features.map((feature, index) => (\n                  <li key={index} className=\"flex items-center space-x-2\">\n                    <CheckIcon className=\"h-4 w-4 text-green-500 flex-shrink-0\" />\n                    <span className=\"text-gray-700\">{feature}</span>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n\n          {/* Payment Section */}\n          <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Payment Details</h2>\n            \n            {/* Order Summary */}\n            <div className=\"bg-gray-50 rounded-xl p-6 mb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Order Summary</h3>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-gray-700\">{currentPlan.name}</span>\n                <span className=\"font-semibold\">${currentPlan.price}</span>\n              </div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-gray-700\">Processing Fee</span>\n                <span className=\"font-semibold\">$0.00</span>\n              </div>\n              <hr className=\"my-4\" />\n              <div className=\"flex justify-between items-center text-lg font-bold\">\n                <span>Total</span>\n                <span>${currentPlan.price} USD</span>\n              </div>\n            </div>\n\n            {/* PayPal Payment */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Payment Method</h3>\n              \n              {isProcessing ? (\n                <div className=\"flex items-center justify-center py-8\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"></div>\n                  <span className=\"text-gray-700\">Processing payment...</span>\n                </div>\n              ) : (\n                <PayPalScriptProvider options={paypalOptions}>\n                  <PayPalButtons\n                    createOrder={createOrder}\n                    onApprove={onApprove}\n                    onError={onError}\n                    style={{\n                      layout: \"vertical\",\n                      color: \"blue\",\n                      shape: \"rect\",\n                      label: \"paypal\"\n                    }}\n                  />\n                </PayPalScriptProvider>\n              )}\n\n              {/* Security Badges */}\n              <div className=\"mt-6 pt-6 border-t border-gray-200\">\n                <div className=\"flex items-center justify-center space-x-6 text-sm text-gray-600\">\n                  <div className=\"flex items-center space-x-1\">\n                    <ShieldCheckIcon className=\"h-4 w-4 text-green-500\" />\n                    <span>SSL Secured</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    <ShieldCheckIcon className=\"h-4 w-4 text-blue-500\" />\n                    <span>PayPal Protected</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    <ShieldCheckIcon className=\"h-4 w-4 text-purple-500\" />\n                    <span>GDPR Compliant</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Money Back Guarantee */}\n        <div className=\"mt-12 text-center bg-white rounded-2xl shadow-lg p-8\">\n          <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n            30-Day Money Back Guarantee\n          </h3>\n          <p className=\"text-gray-600 max-w-2xl mx-auto\">\n            We're confident you'll love our verification services. If you're not completely satisfied \n            with your results, we'll provide a full refund within 30 days of purchase.\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CheckoutPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;;;AALA;;;;;AAOA,MAAM,eAAe;;IACnB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,QAAQ;QACZ,OAAO;YACL,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA,MAAM;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA,WAAW;YACT,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,OAAO,aAAa,GAAG,CAAC;YAC9B,IAAI,QAAQ,KAAK,CAAC,KAA2B,EAAE;gBAC7C,gBAAgB;YAClB;QACF;iCAAG;QAAC;KAAa;IAEjB,MAAM,cAAc,KAAK,CAAC,aAAmC;IAE7D,MAAM,gBAAgB;QACpB,UAAU;QACV,UAAU;QACV,QAAQ;QACR,eAAe;IACjB;IAEA,MAAM,cAAc,CAAC,MAAW;QAC9B,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC;YAC1B,gBAAgB;gBACd;oBACE,QAAQ;wBACN,OAAO,YAAY,KAAK;wBACxB,eAAe;oBACjB;oBACA,aAAa,AAAC,gBAAgC,OAAjB,YAAY,IAAI;gBAC/C;aACD;QACH;IACF;IAEA,MAAM,YAAY,OAAO,MAAW;QAClC,gBAAgB;QAChB,IAAI;YACF,MAAM,UAAU,MAAM,QAAQ,KAAK,CAAC,OAAO;YAC3C,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,2CAA2C;YAC3C,WAAW;gBACT,gBAAgB;gBAChB,6CAA6C;gBAC7C,OAAO,IAAI,CAAC;YACd,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,gBAAgB;YAChB,MAAM;QACR;IACF;IAEA,MAAM,UAAU,CAAC;QACf,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,MAAM;IACR;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,8NAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;;;;;;sCAE7B,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAEtD,6LAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,OAAO,GAAG,CAAC;4CAAC,CAAC,KAAK,KAAK;6DACrC,6LAAC;4CAEC,WAAW,AAAC,yEAIX,OAHC,iBAAiB,MACb,+BACA;4CAEN,SAAS,IAAM,gBAAgB;sDAE/B,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAuC,KAAK,IAAI;;;;;;0EAC9D,6LAAC;gEAAE,WAAU;0EAAiB,KAAK,WAAW;;;;;;;;;;;;kEAEhD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEAAmC;oEAC9C,KAAK,KAAK;oEACX,QAAQ,6BACP,6LAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;0EAGxD,6LAAC;gEAAI,WAAW,AAAC,sCAIhB,OAHC,iBAAiB,MACb,gCACA;0EAEH,iBAAiB,qBAChB,6LAAC,kNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CA1BxB;;;;;;;;;;;8CAoCX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDACX,YAAY,IAAI;gDAAC;;;;;;;sDAEpB,6LAAC;4CAAG,WAAU;sDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAClC,6LAAC;oDAAe,WAAU;;sEACxB,6LAAC,kNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;mDAF1B;;;;;;;;;;;;;;;;;;;;;;sCAUjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAiB,YAAY,IAAI;;;;;;8DACjD,6LAAC;oDAAK,WAAU;;wDAAgB;wDAAE,YAAY,KAAK;;;;;;;;;;;;;sDAErD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAG,WAAU;;;;;;sDACd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;;wDAAK;wDAAE,YAAY,KAAK;wDAAC;;;;;;;;;;;;;;;;;;;8CAK9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;wCAEnD,6BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;qGAGlC,6LAAC,4LAAA,CAAA,uBAAoB;4CAAC,SAAS;sDAC7B,cAAA,6LAAC,4LAAA,CAAA,gBAAa;gDACZ,aAAa;gDACb,WAAW;gDACX,SAAS;gDACT,OAAO;oDACL,QAAQ;oDACR,OAAO;oDACP,OAAO;oDACP,OAAO;gDACT;;;;;;;;;;;sDAMN,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8NAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;;0EAC3B,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8NAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;;0EAC3B,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8NAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;;0EAC3B,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuC;;;;;;sCAGrD,6LAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;;;;;;;;;;;;AAQzD;GArQM;;QACW,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAFhC;uCAuQS", "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40paypal/react-paypal-js/dist/esm/react-paypal-js.js"], "sourcesContent": ["/*!\n * react-paypal-js v8.8.3 (2025-04-11T19:50:46.506Z)\n * Copyright 2020-present, PayPal, Inc. All rights reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport React, { createContext, useContext, useRef, useState, useEffect, useReducer } from 'react';\n\n/**\n * Enum for the SDK script resolve status,\n *\n * @enum {string}\n */\nvar SCRIPT_LOADING_STATE;\n(function (SCRIPT_LOADING_STATE) {\n  SCRIPT_LOADING_STATE[\"INITIAL\"] = \"initial\";\n  SCRIPT_LOADING_STATE[\"PENDING\"] = \"pending\";\n  SCRIPT_LOADING_STATE[\"REJECTED\"] = \"rejected\";\n  SCRIPT_LOADING_STATE[\"RESOLVED\"] = \"resolved\";\n})(SCRIPT_LOADING_STATE || (SCRIPT_LOADING_STATE = {}));\n/**\n * Enum for the PayPalScriptProvider context dispatch actions\n *\n * @enum {string}\n */\nvar DISPATCH_ACTION;\n(function (DISPATCH_ACTION) {\n  DISPATCH_ACTION[\"LOADING_STATUS\"] = \"setLoadingStatus\";\n  DISPATCH_ACTION[\"RESET_OPTIONS\"] = \"resetOptions\";\n  DISPATCH_ACTION[\"SET_BRAINTREE_INSTANCE\"] = \"braintreeInstance\";\n})(DISPATCH_ACTION || (DISPATCH_ACTION = {}));\n/**\n * Enum for all the available hosted fields\n *\n * @enum {string}\n */\nvar PAYPAL_HOSTED_FIELDS_TYPES;\n(function (PAYPAL_HOSTED_FIELDS_TYPES) {\n  PAYPAL_HOSTED_FIELDS_TYPES[\"NUMBER\"] = \"number\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"CVV\"] = \"cvv\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_DATE\"] = \"expirationDate\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_MONTH\"] = \"expirationMonth\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_YEAR\"] = \"expirationYear\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"POSTAL_CODE\"] = \"postalCode\";\n})(PAYPAL_HOSTED_FIELDS_TYPES || (PAYPAL_HOSTED_FIELDS_TYPES = {}));\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nfunction __rest$1(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n}\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\n/*********************************************\n * Common reference to the script identifier *\n *********************************************/\n// keep this script id value in kebab-case format\nvar SCRIPT_ID = \"data-react-paypal-script-id\";\nvar SDK_SETTINGS = {\n  DATA_CLIENT_TOKEN: \"dataClientToken\",\n  DATA_JS_SDK_LIBRARY: \"dataJsSdkLibrary\",\n  DATA_LIBRARY_VALUE: \"react-paypal-js\",\n  DATA_NAMESPACE: \"dataNamespace\",\n  DATA_SDK_INTEGRATION_SOURCE: \"dataSdkIntegrationSource\",\n  DATA_USER_ID_TOKEN: \"dataUserIdToken\"\n};\nvar LOAD_SCRIPT_ERROR = \"Failed to load the PayPal JS SDK script.\";\n/****************************\n * Braintree error messages *\n ****************************/\nvar EMPTY_BRAINTREE_AUTHORIZATION_ERROR_MESSAGE = \"Invalid authorization data. Use dataClientToken or dataUserIdToken to authorize.\";\nvar braintreeVersion = \"3.117.0\";\nvar BRAINTREE_SOURCE = \"https://js.braintreegateway.com/web/\".concat(braintreeVersion, \"/js/client.min.js\");\nvar BRAINTREE_PAYPAL_CHECKOUT_SOURCE = \"https://js.braintreegateway.com/web/\".concat(braintreeVersion, \"/js/paypal-checkout.min.js\");\n/*********************\n * PayPal namespaces *\n *********************/\nvar DEFAULT_PAYPAL_NAMESPACE = \"paypal\";\nvar DEFAULT_BRAINTREE_NAMESPACE = \"braintree\";\n/*****************\n * Hosted Fields *\n *****************/\nvar HOSTED_FIELDS_CHILDREN_ERROR = \"To use HostedFields you must use it with at least 3 children with types: [number, cvv, expirationDate] includes\";\nvar HOSTED_FIELDS_DUPLICATE_CHILDREN_ERROR = \"Cannot use duplicate HostedFields as children\";\n/*******************\n * Script Provider *\n *******************/\nvar SCRIPT_PROVIDER_REDUCER_ERROR = \"usePayPalScriptReducer must be used within a PayPalScriptProvider\";\nvar CARD_FIELDS_DUPLICATE_CHILDREN_ERROR = \"Cannot use duplicate CardFields as children\";\nvar CARD_FIELDS_CONTEXT_ERROR = \"Individual CardFields must be rendered inside the PayPalCardFieldsProvider\";\n\n/**\n * Get the namespace from the window in the browser\n * this is useful to get the paypal object from window\n * after load PayPal SDK script\n *\n * @param namespace the name space to return\n * @returns the namespace if exists or undefined otherwise\n */\nfunction getPayPalWindowNamespace$1(namespace) {\n  if (namespace === void 0) {\n    namespace = DEFAULT_PAYPAL_NAMESPACE;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return window[namespace];\n}\n/**\n * Get a namespace from the window in the browser\n * this is useful to get the braintree from window\n * after load Braintree script\n *\n * @param namespace the name space to return\n * @returns the namespace if exists or undefined otherwise\n */\nfunction getBraintreeWindowNamespace(namespace) {\n  if (namespace === void 0) {\n    namespace = DEFAULT_BRAINTREE_NAMESPACE;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return window[namespace];\n}\n/**\n * Creates a string hash code based on the string argument\n *\n * @param str the source input string to hash\n * @returns string hash code\n */\nfunction hashStr(str) {\n  var hash = \"\";\n  for (var i = 0; i < str.length; i++) {\n    var total = str[i].charCodeAt(0) * i;\n    if (str[i + 1]) {\n      total += str[i + 1].charCodeAt(0) * (i - 1);\n    }\n    hash += String.fromCharCode(97 + Math.abs(total) % 26);\n  }\n  return hash;\n}\nfunction generateErrorMessage(_a) {\n  var reactComponentName = _a.reactComponentName,\n    sdkComponentKey = _a.sdkComponentKey,\n    _b = _a.sdkRequestedComponents,\n    sdkRequestedComponents = _b === void 0 ? \"\" : _b,\n    _c = _a.sdkDataNamespace,\n    sdkDataNamespace = _c === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _c;\n  var requiredOptionCapitalized = sdkComponentKey.charAt(0).toUpperCase().concat(sdkComponentKey.substring(1));\n  var errorMessage = \"Unable to render <\".concat(reactComponentName, \" /> because window.\").concat(sdkDataNamespace, \".\").concat(requiredOptionCapitalized, \" is undefined.\");\n  // The JS SDK only loads the buttons component by default.\n  // All other components like messages and marks must be requested using the \"components\" query parameter\n  var requestedComponents = typeof sdkRequestedComponents === \"string\" ? sdkRequestedComponents : sdkRequestedComponents.join(\",\");\n  if (!requestedComponents.includes(sdkComponentKey)) {\n    var expectedComponents = [requestedComponents, sdkComponentKey].filter(Boolean).join();\n    errorMessage += \"\\nTo fix the issue, add '\".concat(sdkComponentKey, \"' to the list of components passed to the parent PayPalScriptProvider:\") + \"\\n`<PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>`.\");\n  }\n  return errorMessage;\n}\n\n/**\n * Generate a new random identifier for react-paypal-js\n *\n * @returns the {@code string} containing the random library name\n */\nfunction getScriptID(options) {\n  // exclude the data-react-paypal-script-id value from the options hash\n  var _a = options,\n    _b = SCRIPT_ID;\n  _a[_b];\n  var paypalScriptOptions = __rest$1(_a, [_b + \"\"]);\n  return \"react-paypal-js-\".concat(hashStr(JSON.stringify(paypalScriptOptions)));\n}\n/**\n * Destroy the PayPal SDK from the document page\n *\n * @param reactPayPalScriptID the script identifier\n */\nfunction destroySDKScript(reactPayPalScriptID) {\n  var scriptNode = self.document.querySelector(\"script[\".concat(SCRIPT_ID, \"=\\\"\").concat(reactPayPalScriptID, \"\\\"]\"));\n  if (scriptNode === null || scriptNode === void 0 ? void 0 : scriptNode.parentNode) {\n    scriptNode.parentNode.removeChild(scriptNode);\n  }\n}\n/**\n * Reducer function to handle complex state changes on the context\n *\n * @param state  the current state on the context object\n * @param action the action to be executed on the previous state\n * @returns a the same state if the action wasn't found, or a new state otherwise\n */\nfunction scriptReducer(state, action) {\n  var _a, _b;\n  switch (action.type) {\n    case DISPATCH_ACTION.LOADING_STATUS:\n      if (typeof action.value === \"object\") {\n        return __assign(__assign({}, state), {\n          loadingStatus: action.value.state,\n          loadingStatusErrorMessage: action.value.message\n        });\n      }\n      return __assign(__assign({}, state), {\n        loadingStatus: action.value\n      });\n    case DISPATCH_ACTION.RESET_OPTIONS:\n      // destroy existing script to make sure only one script loads at a time\n      destroySDKScript(state.options[SCRIPT_ID]);\n      return __assign(__assign({}, state), {\n        loadingStatus: SCRIPT_LOADING_STATE.PENDING,\n        options: __assign(__assign((_a = {}, _a[SDK_SETTINGS.DATA_SDK_INTEGRATION_SOURCE] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _a), action.value), (_b = {}, _b[SCRIPT_ID] = \"\".concat(getScriptID(action.value)), _b))\n      });\n    case DISPATCH_ACTION.SET_BRAINTREE_INSTANCE:\n      return __assign(__assign({}, state), {\n        braintreePayPalCheckoutInstance: action.value\n      });\n    default:\n      {\n        return state;\n      }\n  }\n}\n// Create the React context to use in the script provider component\nvar ScriptContext = createContext(null);\n\n/**\n * Check if the context is valid and ready to dispatch actions.\n *\n * @param scriptContext the result of connecting to the context provider\n * @returns strict context avoiding null values in the type\n */\nfunction validateReducer(scriptContext) {\n  if (typeof (scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.dispatch) === \"function\" && scriptContext.dispatch.length !== 0) {\n    return scriptContext;\n  }\n  throw new Error(SCRIPT_PROVIDER_REDUCER_ERROR);\n}\n/**\n * Check if the dataClientToken or the dataUserIdToken are\n * set in the options of the context.\n * @type dataClientToken is use to pass a client token\n * @type dataUserIdToken is use to pass a client tokenization key\n *\n * @param scriptContext the result of connecting to the context provider\n * @throws an {@link Error} if both dataClientToken and the dataUserIdToken keys are null or undefined\n * @returns strict context if one of the keys are defined\n */\nvar validateBraintreeAuthorizationData = function (scriptContext) {\n  var _a, _b;\n  if (!((_a = scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.options) === null || _a === void 0 ? void 0 : _a[SDK_SETTINGS.DATA_CLIENT_TOKEN]) && !((_b = scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.options) === null || _b === void 0 ? void 0 : _b[SDK_SETTINGS.DATA_USER_ID_TOKEN])) {\n    throw new Error(EMPTY_BRAINTREE_AUTHORIZATION_ERROR_MESSAGE);\n  }\n  return scriptContext;\n};\n\n/**\n * Custom hook to get access to the Script context and\n * dispatch actions to modify the state on the {@link ScriptProvider} component\n *\n * @returns a tuple containing the state of the context and\n * a dispatch function to modify the state\n */\nfunction usePayPalScriptReducer() {\n  var scriptContext = validateReducer(useContext(ScriptContext));\n  var derivedStatusContext = __assign(__assign({}, scriptContext), {\n    isInitial: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.INITIAL,\n    isPending: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.PENDING,\n    isResolved: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.RESOLVED,\n    isRejected: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.REJECTED\n  });\n  return [derivedStatusContext, scriptContext.dispatch];\n}\n/**\n * Custom hook to get access to the ScriptProvider context\n *\n * @returns the latest state of the context\n */\nfunction useScriptProviderContext() {\n  var scriptContext = validateBraintreeAuthorizationData(validateReducer(useContext(ScriptContext)));\n  return [scriptContext, scriptContext.dispatch];\n}\n\n// Create the React context to use in the PayPal hosted fields provider\nvar PayPalHostedFieldsContext = createContext({});\n\n/**\n * Custom hook to get access to the PayPal Hosted Fields instance.\n * The instance represent the returned object after the render process\n * With this object a user can submit the fields and dynamically modify the cards\n *\n * @returns the hosted fields instance if is available in the component\n */\nfunction usePayPalHostedFields() {\n  return useContext(PayPalHostedFieldsContext);\n}\nfunction useProxyProps(props) {\n  var proxyRef = useRef(new Proxy({}, {\n    get: function (target, prop, receiver) {\n      /**\n       *\n       * If target[prop] is a function, return a function that accesses\n       * this function off the target object. We can mutate the target with\n       * new copies of this function without having to re-render the\n       * SDK components to pass new callbacks.\n       *\n       * */\n      if (typeof target[prop] === \"function\") {\n        return function () {\n          var args = [];\n          for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n          }\n          // eslint-disable-next-line @typescript-eslint/ban-types\n          return target[prop].apply(target, args);\n        };\n      }\n      return Reflect.get(target, prop, receiver);\n    }\n  }));\n  proxyRef.current = Object.assign(proxyRef.current, props);\n  return proxyRef.current;\n}\n\n/**\nThis `<PayPalButtons />` component supports rendering [buttons](https://developer.paypal.com/docs/business/javascript-sdk/javascript-sdk-reference/#buttons) for PayPal, Venmo, and alternative payment methods.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n*/\nvar PayPalButtons = function (_a) {\n  var _b;\n  var _c = _a.className,\n    className = _c === void 0 ? \"\" : _c,\n    _d = _a.disabled,\n    disabled = _d === void 0 ? false : _d,\n    children = _a.children,\n    _e = _a.forceReRender,\n    forceReRender = _e === void 0 ? [] : _e,\n    buttonProps = __rest$1(_a, [\"className\", \"disabled\", \"children\", \"forceReRender\"]);\n  var isDisabledStyle = disabled ? {\n    opacity: 0.38\n  } : {};\n  var classNames = \"\".concat(className, \" \").concat(disabled ? \"paypal-buttons-disabled\" : \"\").trim();\n  var buttonsContainerRef = useRef(null);\n  var buttons = useRef(null);\n  var proxyProps = useProxyProps(buttonProps);\n  var _f = usePayPalScriptReducer()[0],\n    isResolved = _f.isResolved,\n    options = _f.options;\n  var _g = useState(null),\n    initActions = _g[0],\n    setInitActions = _g[1];\n  var _h = useState(true),\n    isEligible = _h[0],\n    setIsEligible = _h[1];\n  var _j = useState(null),\n    setErrorState = _j[1];\n  function closeButtonsComponent() {\n    if (buttons.current !== null) {\n      buttons.current.close().catch(function () {\n        // ignore errors when closing the component\n      });\n    }\n  }\n  if ((_b = buttons.current) === null || _b === void 0 ? void 0 : _b.updateProps) {\n    buttons.current.updateProps({\n      message: buttonProps.message\n    });\n  }\n  // useEffect hook for rendering the buttons\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return closeButtonsComponent;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options.dataNamespace);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Buttons === undefined) {\n      setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalButtons.displayName,\n          sdkComponentKey: \"buttons\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n      return closeButtonsComponent;\n    }\n    var decoratedOnInit = function (data, actions) {\n      setInitActions(actions);\n      if (typeof buttonProps.onInit === \"function\") {\n        buttonProps.onInit(data, actions);\n      }\n    };\n    try {\n      buttons.current = paypalWindowNamespace.Buttons(__assign(__assign({}, proxyProps), {\n        onInit: decoratedOnInit\n      }));\n    } catch (err) {\n      return setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalButtons /> component. Failed to initialize:  \".concat(err));\n      });\n    }\n    // only render the button when eligible\n    if (buttons.current.isEligible() === false) {\n      setIsEligible(false);\n      return closeButtonsComponent;\n    }\n    if (!buttonsContainerRef.current) {\n      return closeButtonsComponent;\n    }\n    buttons.current.render(buttonsContainerRef.current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (buttonsContainerRef.current === null || buttonsContainerRef.current.children.length === 0) {\n        // paypal buttons container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal buttons container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalButtons /> component. \".concat(err));\n      });\n    });\n    return closeButtonsComponent;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, __spreadArray(__spreadArray([isResolved], forceReRender, true), [buttonProps.fundingSource], false));\n  // useEffect hook for managing disabled state\n  useEffect(function () {\n    if (initActions === null) {\n      return;\n    }\n    if (disabled === true) {\n      initActions.disable().catch(function () {\n        // ignore errors when disabling the component\n      });\n    } else {\n      initActions.enable().catch(function () {\n        // ignore errors when enabling the component\n      });\n    }\n  }, [disabled, initActions]);\n  return React.createElement(React.Fragment, null, isEligible ? React.createElement(\"div\", {\n    ref: buttonsContainerRef,\n    style: isDisabledStyle,\n    className: classNames\n  }) : children);\n};\nPayPalButtons.displayName = \"PayPalButtons\";\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\nfunction findScript(url, attributes) {\n  var currentScript = document.querySelector(\"script[src=\\\"\".concat(url, \"\\\"]\"));\n  if (currentScript === null) return null;\n  var nextScript = createScriptElement(url, attributes);\n  var currentScriptClone = currentScript.cloneNode();\n  delete currentScriptClone.dataset.uidAuto;\n  if (Object.keys(currentScriptClone.dataset).length !== Object.keys(nextScript.dataset).length) {\n    return null;\n  }\n  var isExactMatch = true;\n  Object.keys(currentScriptClone.dataset).forEach(function (key) {\n    if (currentScriptClone.dataset[key] !== nextScript.dataset[key]) {\n      isExactMatch = false;\n    }\n  });\n  return isExactMatch ? currentScript : null;\n}\nfunction insertScriptElement(_a) {\n  var url = _a.url,\n    attributes = _a.attributes,\n    onSuccess = _a.onSuccess,\n    onError = _a.onError;\n  var newScript = createScriptElement(url, attributes);\n  newScript.onerror = onError;\n  newScript.onload = onSuccess;\n  document.head.insertBefore(newScript, document.head.firstElementChild);\n}\nfunction processOptions(_a) {\n  var customSdkBaseUrl = _a.sdkBaseUrl,\n    environment = _a.environment,\n    options = __rest(_a, [\"sdkBaseUrl\", \"environment\"]);\n  var sdkBaseUrl = customSdkBaseUrl || processSdkBaseUrl(environment);\n  var optionsWithStringIndex = options;\n  var _b = Object.keys(optionsWithStringIndex).filter(function (key) {\n      return typeof optionsWithStringIndex[key] !== \"undefined\" && optionsWithStringIndex[key] !== null && optionsWithStringIndex[key] !== \"\";\n    }).reduce(function (accumulator, key) {\n      var value = optionsWithStringIndex[key].toString();\n      key = camelCaseToKebabCase(key);\n      if (key.substring(0, 4) === \"data\" || key === \"crossorigin\") {\n        accumulator.attributes[key] = value;\n      } else {\n        accumulator.queryParams[key] = value;\n      }\n      return accumulator;\n    }, {\n      queryParams: {},\n      attributes: {}\n    }),\n    queryParams = _b.queryParams,\n    attributes = _b.attributes;\n  if (queryParams[\"merchant-id\"] && queryParams[\"merchant-id\"].indexOf(\",\") !== -1) {\n    attributes[\"data-merchant-id\"] = queryParams[\"merchant-id\"];\n    queryParams[\"merchant-id\"] = \"*\";\n  }\n  return {\n    url: \"\".concat(sdkBaseUrl, \"?\").concat(objectToQueryString(queryParams)),\n    attributes: attributes\n  };\n}\nfunction camelCaseToKebabCase(str) {\n  var replacer = function (match, indexOfMatch) {\n    return (indexOfMatch ? \"-\" : \"\") + match.toLowerCase();\n  };\n  return str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, replacer);\n}\nfunction objectToQueryString(params) {\n  var queryString = \"\";\n  Object.keys(params).forEach(function (key) {\n    if (queryString.length !== 0) queryString += \"&\";\n    queryString += key + \"=\" + params[key];\n  });\n  return queryString;\n}\nfunction processSdkBaseUrl(environment) {\n  return environment === \"sandbox\" ? \"https://www.sandbox.paypal.com/sdk/js\" : \"https://www.paypal.com/sdk/js\";\n}\nfunction createScriptElement(url, attributes) {\n  if (attributes === void 0) {\n    attributes = {};\n  }\n  var newScript = document.createElement(\"script\");\n  newScript.src = url;\n  Object.keys(attributes).forEach(function (key) {\n    newScript.setAttribute(key, attributes[key]);\n    if (key === \"data-csp-nonce\") {\n      newScript.setAttribute(\"nonce\", attributes[\"data-csp-nonce\"]);\n    }\n  });\n  return newScript;\n}\nfunction loadScript(options, PromisePonyfill) {\n  if (PromisePonyfill === void 0) {\n    PromisePonyfill = Promise;\n  }\n  validateArguments(options, PromisePonyfill);\n  if (typeof document === \"undefined\") return PromisePonyfill.resolve(null);\n  var _a = processOptions(options),\n    url = _a.url,\n    attributes = _a.attributes;\n  var namespace = attributes[\"data-namespace\"] || \"paypal\";\n  var existingWindowNamespace = getPayPalWindowNamespace(namespace);\n  if (!attributes[\"data-js-sdk-library\"]) {\n    attributes[\"data-js-sdk-library\"] = \"paypal-js\";\n  }\n  if (findScript(url, attributes) && existingWindowNamespace) {\n    return PromisePonyfill.resolve(existingWindowNamespace);\n  }\n  return loadCustomScript({\n    url: url,\n    attributes: attributes\n  }, PromisePonyfill).then(function () {\n    var newWindowNamespace = getPayPalWindowNamespace(namespace);\n    if (newWindowNamespace) {\n      return newWindowNamespace;\n    }\n    throw new Error(\"The window.\".concat(namespace, \" global variable is not available.\"));\n  });\n}\nfunction loadCustomScript(options, PromisePonyfill) {\n  if (PromisePonyfill === void 0) {\n    PromisePonyfill = Promise;\n  }\n  validateArguments(options, PromisePonyfill);\n  var url = options.url,\n    attributes = options.attributes;\n  if (typeof url !== \"string\" || url.length === 0) {\n    throw new Error(\"Invalid url.\");\n  }\n  if (typeof attributes !== \"undefined\" && typeof attributes !== \"object\") {\n    throw new Error(\"Expected attributes to be an object.\");\n  }\n  return new PromisePonyfill(function (resolve, reject) {\n    if (typeof document === \"undefined\") return resolve();\n    insertScriptElement({\n      url: url,\n      attributes: attributes,\n      onSuccess: function () {\n        return resolve();\n      },\n      onError: function () {\n        var defaultError = new Error(\"The script \\\"\".concat(url, \"\\\" failed to load. Check the HTTP status code and response body in DevTools to learn more.\"));\n        return reject(defaultError);\n      }\n    });\n  });\n}\nfunction getPayPalWindowNamespace(namespace) {\n  return window[namespace];\n}\nfunction validateArguments(options, PromisePonyfill) {\n  if (typeof options !== \"object\" || options === null) {\n    throw new Error(\"Expected an options object.\");\n  }\n  var environment = options.environment;\n  if (environment && environment !== \"production\" && environment !== \"sandbox\") {\n    throw new Error('The `environment` option must be either \"production\" or \"sandbox\".');\n  }\n  if (typeof PromisePonyfill !== \"undefined\" && typeof PromisePonyfill !== \"function\") {\n    throw new Error(\"Expected PromisePonyfill to be a function.\");\n  }\n}\n\n/**\n * Simple check to determine if the Braintree is a valid namespace.\n *\n * @param braintreeSource the source {@link BraintreeNamespace}\n * @returns a boolean representing if the namespace is valid.\n */\nvar isValidBraintreeNamespace = function (braintreeSource) {\n  var _a, _b;\n  if (typeof ((_a = braintreeSource === null || braintreeSource === void 0 ? void 0 : braintreeSource.client) === null || _a === void 0 ? void 0 : _a.create) !== \"function\" && typeof ((_b = braintreeSource === null || braintreeSource === void 0 ? void 0 : braintreeSource.paypalCheckout) === null || _b === void 0 ? void 0 : _b.create) !== \"function\") {\n    throw new Error(\"The braintreeNamespace property is not a valid BraintreeNamespace type.\");\n  }\n  return true;\n};\n/**\n * Use `actions.braintree` to provide an interface for the paypalCheckoutInstance\n * through the createOrder, createBillingAgreement and onApprove callbacks\n *\n * @param braintreeButtonProps the component button options\n * @returns a new copy of the component button options casted as {@link PayPalButtonsComponentProps}\n */\nvar decorateActions = function (buttonProps, payPalCheckoutInstance) {\n  var createOrderRef = buttonProps.createOrder;\n  var createBillingAgreementRef = buttonProps.createBillingAgreement;\n  var onApproveRef = buttonProps.onApprove;\n  if (typeof createOrderRef === \"function\") {\n    buttonProps.createOrder = function (data, actions) {\n      return createOrderRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  if (typeof createBillingAgreementRef === \"function\") {\n    buttonProps.createBillingAgreement = function (data, actions) {\n      return createBillingAgreementRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  if (typeof onApproveRef === \"function\") {\n    buttonProps.onApprove = function (data, actions) {\n      return onApproveRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  return __assign({}, buttonProps);\n};\n/**\n * Get the Braintree namespace from the component props.\n * If the prop `braintreeNamespace` is undefined will try to load it from the CDN.\n * This function allows users to set the braintree manually on the `BraintreePayPalButtons` component.\n *\n * Use case can be for example legacy sites using AMD/UMD modules,\n * trying to integrate the `BraintreePayPalButtons` component.\n * If we attempt to load the Braintree from the CDN won't define the braintree namespace.\n * This happens because the braintree script is an UMD module.\n * After detecting the AMD on the global scope will create an anonymous module using `define`\n * and the `BraintreePayPalButtons` won't be able to get access to the `window.braintree` namespace\n * from the global context.\n *\n * @param braintreeSource the source {@link BraintreeNamespace}\n * @returns the {@link BraintreeNamespace}\n */\nvar getBraintreeNamespace = function (braintreeSource) {\n  if (braintreeSource && isValidBraintreeNamespace(braintreeSource)) {\n    return Promise.resolve(braintreeSource);\n  }\n  return Promise.all([loadCustomScript({\n    url: BRAINTREE_SOURCE\n  }), loadCustomScript({\n    url: BRAINTREE_PAYPAL_CHECKOUT_SOURCE\n  })]).then(function () {\n    return getBraintreeWindowNamespace();\n  });\n};\n\n/**\nThis `<BraintreePayPalButtons />` component renders the [Braintree PayPal Buttons](https://developer.paypal.com/braintree/docs/guides/paypal/overview) for Braintree Merchants.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n\nNote: You are able to make your integration using the client token or using the tokenization key.\n\n- To use the client token integration set the key `dataClientToken` in the `PayPayScriptProvider` component's options.\n- To use the tokenization key integration set the key `dataUserIdToken` in the `PayPayScriptProvider` component's options.\n*/\nvar BraintreePayPalButtons = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    _c = _a.disabled,\n    disabled = _c === void 0 ? false : _c,\n    children = _a.children,\n    _d = _a.forceReRender,\n    forceReRender = _d === void 0 ? [] : _d,\n    braintreeNamespace = _a.braintreeNamespace,\n    merchantAccountId = _a.merchantAccountId,\n    buttonProps = __rest$1(_a, [\"className\", \"disabled\", \"children\", \"forceReRender\", \"braintreeNamespace\", \"merchantAccountId\"]);\n  var _e = useState(null),\n    setErrorState = _e[1];\n  var _f = useScriptProviderContext(),\n    providerContext = _f[0],\n    dispatch = _f[1];\n  useEffect(function () {\n    getBraintreeNamespace(braintreeNamespace).then(function (braintree) {\n      var clientTokenizationKey = providerContext.options[SDK_SETTINGS.DATA_USER_ID_TOKEN];\n      var clientToken = providerContext.options[SDK_SETTINGS.DATA_CLIENT_TOKEN];\n      return braintree.client.create({\n        authorization: clientTokenizationKey || clientToken\n      }).then(function (clientInstance) {\n        var merchantProp = merchantAccountId ? {\n          merchantAccountId: merchantAccountId\n        } : {};\n        return braintree.paypalCheckout.create(__assign(__assign({}, merchantProp), {\n          client: clientInstance\n        }));\n      }).then(function (paypalCheckoutInstance) {\n        dispatch({\n          type: DISPATCH_ACTION.SET_BRAINTREE_INSTANCE,\n          value: paypalCheckoutInstance\n        });\n      });\n    }).catch(function (err) {\n      setErrorState(function () {\n        throw new Error(\"\".concat(LOAD_SCRIPT_ERROR, \" \").concat(err));\n      });\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [providerContext.options]);\n  return React.createElement(React.Fragment, null, providerContext.braintreePayPalCheckoutInstance && React.createElement(PayPalButtons, __assign({\n    className: className,\n    disabled: disabled,\n    forceReRender: forceReRender\n  }, decorateActions(buttonProps, providerContext.braintreePayPalCheckoutInstance)), children));\n};\n\n/**\nThe `<PayPalMarks />` component is used for conditionally rendering different payment options using radio buttons.\nThe [Display PayPal Buttons with other Payment Methods guide](https://developer.paypal.com/docs/business/checkout/add-capabilities/buyer-experience/#display-paypal-buttons-with-other-payment-methods) describes this style of integration in detail.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n\nThis component can also be configured to use a single funding source similar to the [standalone buttons](https://developer.paypal.com/docs/business/checkout/configure-payments/standalone-buttons/) approach.\nA `FUNDING` object is exported by this library which has a key for every available funding source option.\n*/\nvar PayPalMarks = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    children = _a.children,\n    markProps = __rest$1(_a, [\"className\", \"children\"]);\n  var _c = usePayPalScriptReducer()[0],\n    isResolved = _c.isResolved,\n    options = _c.options;\n  var markContainerRef = useRef(null);\n  var _d = useState(true),\n    isEligible = _d[0],\n    setIsEligible = _d[1];\n  var _e = useState(null),\n    setErrorState = _e[1];\n  /**\n   * Render PayPal Mark into the DOM\n   */\n  var renderPayPalMark = function (mark) {\n    var current = markContainerRef.current;\n    // only render the mark when eligible\n    if (!current || !mark.isEligible()) {\n      return setIsEligible(false);\n    }\n    // Remove any children before render it again\n    if (current.firstChild) {\n      current.removeChild(current.firstChild);\n    }\n    mark.render(current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (current === null || current.children.length === 0) {\n        // paypal marks container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal marks container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalMarks /> component. \".concat(err));\n      });\n    });\n  };\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Marks === undefined) {\n      return setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalMarks.displayName,\n          sdkComponentKey: \"marks\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n    }\n    renderPayPalMark(paypalWindowNamespace.Marks(__assign({}, markProps)));\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isResolved, markProps.fundingSource]);\n  return React.createElement(React.Fragment, null, isEligible ? React.createElement(\"div\", {\n    ref: markContainerRef,\n    className: className\n  }) : children);\n};\nPayPalMarks.displayName = \"PayPalMarks\";\n\n/**\nThis `<PayPalMessages />` messages component renders a credit messaging on upstream merchant sites.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n*/\nvar PayPalMessages = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    _c = _a.forceReRender,\n    forceReRender = _c === void 0 ? [] : _c,\n    messageProps = __rest$1(_a, [\"className\", \"forceReRender\"]);\n  var _d = usePayPalScriptReducer()[0],\n    isResolved = _d.isResolved,\n    options = _d.options;\n  var messagesContainerRef = useRef(null);\n  var messages = useRef(null);\n  var _e = useState(null),\n    setErrorState = _e[1];\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Messages === undefined) {\n      return setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalMessages.displayName,\n          sdkComponentKey: \"messages\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n    }\n    messages.current = paypalWindowNamespace.Messages(__assign({}, messageProps));\n    messages.current.render(messagesContainerRef.current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (messagesContainerRef.current === null || messagesContainerRef.current.children.length === 0) {\n        // paypal messages container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal messages container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalMessages /> component. \".concat(err));\n      });\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, __spreadArray([isResolved], forceReRender, true));\n  return React.createElement(\"div\", {\n    ref: messagesContainerRef,\n    className: className\n  });\n};\nPayPalMessages.displayName = \"PayPalMessages\";\n\n/**\nThis `<PayPalScriptProvider />` component takes care of loading the JS SDK `<script>`.\nIt manages state for script loading so children components like `<PayPalButtons />` know when it's safe to use the `window.paypal` global namespace.\n\nNote: You always should use this component as a wrapper for  `PayPalButtons`, `PayPalMarks`, `PayPalMessages` and `BraintreePayPalButtons` components.\n */\nvar PayPalScriptProvider = function (_a) {\n  var _b;\n  var _c = _a.options,\n    options = _c === void 0 ? {\n      clientId: \"test\"\n    } : _c,\n    children = _a.children,\n    _d = _a.deferLoading,\n    deferLoading = _d === void 0 ? false : _d;\n  var _e = useReducer(scriptReducer, {\n      options: __assign(__assign({}, options), (_b = {}, _b[SDK_SETTINGS.DATA_JS_SDK_LIBRARY] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _b[SDK_SETTINGS.DATA_SDK_INTEGRATION_SOURCE] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _b[SCRIPT_ID] = \"\".concat(getScriptID(options)), _b)),\n      loadingStatus: deferLoading ? SCRIPT_LOADING_STATE.INITIAL : SCRIPT_LOADING_STATE.PENDING\n    }),\n    state = _e[0],\n    dispatch = _e[1];\n  useEffect(function () {\n    if (deferLoading === false && state.loadingStatus === SCRIPT_LOADING_STATE.INITIAL) {\n      return dispatch({\n        type: DISPATCH_ACTION.LOADING_STATUS,\n        value: SCRIPT_LOADING_STATE.PENDING\n      });\n    }\n    if (state.loadingStatus !== SCRIPT_LOADING_STATE.PENDING) {\n      return;\n    }\n    var isSubscribed = true;\n    loadScript(state.options).then(function () {\n      if (isSubscribed) {\n        dispatch({\n          type: DISPATCH_ACTION.LOADING_STATUS,\n          value: SCRIPT_LOADING_STATE.RESOLVED\n        });\n      }\n    }).catch(function (err) {\n      console.error(\"\".concat(LOAD_SCRIPT_ERROR, \" \").concat(err));\n      if (isSubscribed) {\n        dispatch({\n          type: DISPATCH_ACTION.LOADING_STATUS,\n          value: {\n            state: SCRIPT_LOADING_STATE.REJECTED,\n            message: String(err)\n          }\n        });\n      }\n    });\n    return function () {\n      isSubscribed = false;\n    };\n  }, [state.options, deferLoading, state.loadingStatus]);\n  return React.createElement(ScriptContext.Provider, {\n    value: __assign(__assign({}, state), {\n      dispatch: dispatch\n    })\n  }, children);\n};\n\n/**\n * Custom hook to store registered hosted fields children\n * Each `PayPalHostedField` component should be registered on the parent provider\n *\n * @param initialValue the initially registered components\n * @returns at first, an {@link Object} containing the registered hosted fields,\n * and at the second a function handler to register the hosted fields components\n */\nvar useHostedFieldsRegister = function (initialValue) {\n  if (initialValue === void 0) {\n    initialValue = {};\n  }\n  var registeredFields = useRef(initialValue);\n  var registerHostedField = function (component) {\n    registeredFields.current = __assign(__assign({}, registeredFields.current), component);\n  };\n  return [registeredFields, registerHostedField];\n};\n\n/**\n * Throw an exception if the HostedFields is not found in the paypal namespace\n * Probably cause for this problem is not sending the hosted-fields string\n * as part of the components props in options\n * {@code <PayPalScriptProvider options={{ components: 'hosted-fields'}}>}\n *\n * @param param0 and object containing the components and namespace defined in options\n * @throws {@code Error}\n *\n */\nvar generateMissingHostedFieldsError = function (_a) {\n  var _b = _a.components,\n    components = _b === void 0 ? \"\" : _b,\n    _c = SDK_SETTINGS.DATA_NAMESPACE,\n    _d = _a[_c],\n    dataNamespace = _d === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _d;\n  var expectedComponents = components ? \"\".concat(components, \",hosted-fields\") : \"hosted-fields\";\n  var errorMessage = \"Unable to render <PayPalHostedFieldsProvider /> because window.\".concat(dataNamespace, \".HostedFields is undefined.\");\n  if (!components.includes(\"hosted-fields\")) {\n    errorMessage += \"\\nTo fix the issue, add 'hosted-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>\");\n  }\n  return errorMessage;\n};\n/**\n * Validate the expiration date component. Valid combinations are:\n * 1- Only the `expirationDate` field exists.\n * 2- Only the `expirationMonth` and `expirationYear` fields exist. Cannot be used with the `expirationDate` field.\n *\n * @param registerTypes\n * @returns @type {true} when the children are valid\n */\nvar validateExpirationDate = function (registerTypes) {\n  return !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_DATE) && !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_MONTH) && !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_YEAR);\n};\n/**\n * Check if we find the [number, expiration, cvv] in children\n *\n * @param requiredChildren the list with required children [number, expiration, cvv]\n * @param registerTypes    the list of all the children types pass to the parent\n * @throw an @type {Error} when not find the default children\n */\nvar hasDefaultChildren = function (registerTypes) {\n  if (!registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.NUMBER) || !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.CVV) || validateExpirationDate(registerTypes)) {\n    throw new Error(HOSTED_FIELDS_CHILDREN_ERROR);\n  }\n};\n/**\n * Check if we don't have duplicate children types\n *\n * @param registerTypes the list of all the children types pass to the parent\n * @throw an @type {Error} when duplicate types was found\n */\nvar noDuplicateChildren = function (registerTypes) {\n  if (registerTypes.length !== new Set(registerTypes).size) {\n    throw new Error(HOSTED_FIELDS_DUPLICATE_CHILDREN_ERROR);\n  }\n};\n/**\n * Validate the hosted field children in the PayPalHostedFieldsProvider component.\n * These are the rules:\n * 1- We need to find 3 default children for number, expiration, cvv\n * 2- No duplicate children are allowed\n * 3- No invalid combinations of `expirationDate`, `expirationMonth`, and `expirationYear`\n *\n * @param childrenList     the list of children\n * @param requiredChildren the list with required children [number, expiration, cvv]\n */\nvar validateHostedFieldChildren = function (registeredFields) {\n  hasDefaultChildren(registeredFields);\n  noDuplicateChildren(registeredFields);\n};\n\n/**\nThis `<PayPalHostedFieldsProvider />` provider component wraps the form field elements and accepts props like `createOrder()`.\n\nThis provider component is designed to be used with the `<PayPalHostedField />` component.\n\nWarning: If you don't see anything in the screen probably your client is ineligible.\nTo handle this problem make sure to use the prop `notEligibleError` and pass a component with a custom message.\nTake a look to this link if that is the case: https://developer.paypal.com/docs/checkout/advanced/integrate/\n*/\nvar PayPalHostedFieldsProvider = function (_a) {\n  var styles = _a.styles,\n    createOrder = _a.createOrder,\n    notEligibleError = _a.notEligibleError,\n    children = _a.children,\n    installments = _a.installments;\n  var _b = useScriptProviderContext()[0],\n    options = _b.options,\n    loadingStatus = _b.loadingStatus;\n  var _c = useState(true),\n    isEligible = _c[0],\n    setIsEligible = _c[1];\n  var _d = useState(),\n    cardFields = _d[0],\n    setCardFields = _d[1];\n  var _e = useState(null),\n    setErrorState = _e[1];\n  var hostedFieldsContainerRef = useRef(null);\n  var hostedFields = useRef();\n  var _f = useHostedFieldsRegister(),\n    registeredFields = _f[0],\n    registerHostedField = _f[1];\n  useEffect(function () {\n    var _a;\n    validateHostedFieldChildren(Object.keys(registeredFields.current));\n    // Only render the hosted fields when script is loaded and hostedFields is eligible\n    if (!(loadingStatus === SCRIPT_LOADING_STATE.RESOLVED)) {\n      return;\n    }\n    // Get the hosted fields from the [window.paypal.HostedFields] SDK\n    hostedFields.current = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]).HostedFields;\n    if (!hostedFields.current) {\n      throw new Error(generateMissingHostedFieldsError((_a = {\n        components: options.components\n      }, _a[SDK_SETTINGS.DATA_NAMESPACE] = options[SDK_SETTINGS.DATA_NAMESPACE], _a)));\n    }\n    if (!hostedFields.current.isEligible()) {\n      return setIsEligible(false);\n    }\n    // Clean all the fields before the rerender\n    if (cardFields) {\n      cardFields.teardown();\n    }\n    hostedFields.current.render({\n      // Call your server to set up the transaction\n      createOrder: createOrder,\n      fields: registeredFields.current,\n      installments: installments,\n      styles: styles\n    }).then(function (cardFieldsInstance) {\n      if (hostedFieldsContainerRef.current) {\n        setCardFields(cardFieldsInstance);\n      }\n    }).catch(function (err) {\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalHostedFieldsProvider /> component. \".concat(err));\n      });\n    });\n  }, [loadingStatus, styles]); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", {\n    ref: hostedFieldsContainerRef\n  }, isEligible ? React.createElement(PayPalHostedFieldsContext.Provider, {\n    value: {\n      cardFields: cardFields,\n      registerHostedField: registerHostedField\n    }\n  }, children) : notEligibleError);\n};\n\n/**\nThis `<PayPalHostedField />` component renders individual fields for [Hosted Fields](https://developer.paypal.com/docs/business/checkout/advanced-card-payments/integrate#3-add-javascript-sdk-and-card-form) integrations.\nIt relies on the `<PayPalHostedFieldsProvider />` parent component for managing state related to loading the JS SDK script\nand execute some validations before the rendering the fields.\n\nTo use the PayPal hosted fields you need to define at least three fields:\n\n- A card number field\n- The CVV code from the client card\n- The expiration date\n\nYou can define the expiration date as a single field similar to the example below,\nor you are able to define it in [two separate fields](https://paypal.github.io/react-paypal-js//?path=/docs/paypal-paypalhostedfields--expiration-date). One for the month and second for year.\n\nNote: Take care when using multiple instances of the PayPal Hosted Fields on the same page.\nThe component will fail to render when any of the selectors return more than one element.\n*/\nvar PayPalHostedField = function (_a) {\n  var hostedFieldType = _a.hostedFieldType,\n    // eslint-disable-line @typescript-eslint/no-unused-vars\n    options = _a.options,\n    // eslint-disable-line @typescript-eslint/no-unused-vars\n    props = __rest$1(_a, [\"hostedFieldType\", \"options\"]);\n  var hostedFieldContext = useContext(PayPalHostedFieldsContext);\n  useEffect(function () {\n    var _a;\n    if (!(hostedFieldContext === null || hostedFieldContext === void 0 ? void 0 : hostedFieldContext.registerHostedField)) {\n      throw new Error(\"The HostedField cannot be register in the PayPalHostedFieldsProvider parent component\");\n    }\n    // Register in the parent provider\n    hostedFieldContext.registerHostedField((_a = {}, _a[hostedFieldType] = {\n      selector: options.selector,\n      placeholder: options.placeholder,\n      type: options.type,\n      formatInput: options.formatInput,\n      maskInput: options.maskInput,\n      select: options.select,\n      maxlength: options.maxlength,\n      minlength: options.minlength,\n      prefill: options.prefill,\n      rejectUnsupportedCards: options.rejectUnsupportedCards\n    }, _a));\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", __assign({}, props));\n};\n\n/**\n * Throw an exception if the CardFields is not found in the paypal namespace\n * Probably cause for this problem is not sending the card-fields string\n * as part of the components props in options\n * {@code <PayPalScriptProvider options={{ components: 'card-fields'}}>}\n *\n * @param param0 and object containing the components and namespace defined in options\n * @throws {@code Error}\n *\n */\nvar generateMissingCardFieldsError = function (_a) {\n  var _b = _a.components,\n    components = _b === void 0 ? \"\" : _b,\n    _c = SDK_SETTINGS.DATA_NAMESPACE,\n    _d = _a[_c],\n    dataNamespace = _d === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _d;\n  var expectedComponents = components ? \"\".concat(components, \",card-fields\") : \"card-fields\";\n  var errorMessage = \"Unable to render <PayPalCardFieldsProvider /> because window.\".concat(dataNamespace, \".CardFields is undefined.\");\n  if (!components.includes(\"card-fields\")) {\n    errorMessage += \"\\nTo fix the issue, add 'card-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>\");\n  }\n  return errorMessage;\n};\nfunction ignore() {\n  return;\n}\nfunction hasChildren(container) {\n  var _a;\n  return !!((_a = container.current) === null || _a === void 0 ? void 0 : _a.children.length);\n}\nvar PayPalCardFieldsContext = createContext({\n  cardFieldsForm: null,\n  fields: {},\n  registerField: ignore,\n  unregisterField: ignore // implementation is inside hook and passed through the provider\n});\nvar usePayPalCardFields = function () {\n  return useContext(PayPalCardFieldsContext);\n};\nvar usePayPalCardFieldsRegistry = function () {\n  var _a = useState(null),\n    setError = _a[1];\n  var registeredFields = useRef({});\n  var registerField = function () {\n    var props = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      props[_i] = arguments[_i];\n    }\n    var fieldName = props[0],\n      options = props[1],\n      cardFields = props[2];\n    if (registeredFields.current[fieldName]) {\n      setError(function () {\n        throw new Error(CARD_FIELDS_DUPLICATE_CHILDREN_ERROR);\n      });\n    }\n    registeredFields.current[fieldName] = cardFields === null || cardFields === void 0 ? void 0 : cardFields[fieldName](options);\n    return registeredFields.current[fieldName];\n  };\n  var unregisterField = function (fieldName) {\n    var field = registeredFields.current[fieldName];\n    if (field) {\n      field.close().catch(ignore);\n      delete registeredFields.current[fieldName];\n    }\n  };\n  return {\n    fields: registeredFields.current,\n    registerField: registerField,\n    unregisterField: unregisterField\n  };\n};\nvar FullWidthContainer = function (_a) {\n  var children = _a.children;\n  return React.createElement(\"div\", {\n    style: {\n      width: \"100%\"\n    }\n  }, children);\n};\n\n/**\nThe `<PayPalCardFieldsProvider />` is a context provider that is designed to support the rendering and state management of PayPal CardFields in your application.\n\nThe context provider will initialize the `CardFields` instance from the JS SDK and determine eligibility to render the CardField components. Once the `CardFields` are initialized, the context provider will manage the state of the `CardFields` instance as well as the reference to each individual card field.\n\nPassing the `inputEvents` and `style` props to the context provider will apply them to each of the individual field components.\n\nThe state managed by the provider is accessible through our custom hook `usePayPalCardFields`.\n\n*/\nvar PayPalCardFieldsProvider = function (_a) {\n  var children = _a.children,\n    props = __rest$1(_a, [\"children\"]);\n  var _b = usePayPalScriptReducer()[0],\n    isResolved = _b.isResolved,\n    options = _b.options;\n  var _c = usePayPalCardFieldsRegistry(),\n    fields = _c.fields,\n    registerField = _c.registerField,\n    unregisterField = _c.unregisterField;\n  var _d = useState(null),\n    cardFieldsForm = _d[0],\n    setCardFieldsForm = _d[1];\n  var cardFieldsInstance = useRef(null);\n  var _e = useState(false),\n    isEligible = _e[0],\n    setIsEligible = _e[1];\n  // We set the error inside state so that it can be caught by React's error boundary\n  var _f = useState(null),\n    setError = _f[1];\n  useEffect(function () {\n    var _a, _b, _c;\n    if (!isResolved) {\n      return;\n    }\n    try {\n      cardFieldsInstance.current = (_c = (_b = (_a = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE])).CardFields) === null || _b === void 0 ? void 0 : _b.call(_a, __assign({}, props))) !== null && _c !== void 0 ? _c : null;\n    } catch (error) {\n      setError(function () {\n        throw new Error(\"Failed to render <PayPalCardFieldsProvider /> component. Failed to initialize:  \".concat(error));\n      });\n      return;\n    }\n    if (!cardFieldsInstance.current) {\n      setError(function () {\n        var _a;\n        throw new Error(generateMissingCardFieldsError((_a = {\n          components: options.components\n        }, _a[SDK_SETTINGS.DATA_NAMESPACE] = options[SDK_SETTINGS.DATA_NAMESPACE], _a)));\n      });\n      return;\n    }\n    setIsEligible(cardFieldsInstance.current.isEligible());\n    setCardFieldsForm(cardFieldsInstance.current);\n    return function () {\n      setCardFieldsForm(null);\n      cardFieldsInstance.current = null;\n    };\n  }, [isResolved]); // eslint-disable-line react-hooks/exhaustive-deps\n  if (!isEligible) {\n    // TODO: What should be returned here?\n    return React.createElement(\"div\", null);\n  }\n  return React.createElement(FullWidthContainer, null, React.createElement(PayPalCardFieldsContext.Provider, {\n    value: {\n      cardFieldsForm: cardFieldsForm,\n      fields: fields,\n      registerField: registerField,\n      unregisterField: unregisterField\n    }\n  }, children));\n};\nvar PayPalCardField = function (_a) {\n  var className = _a.className,\n    fieldName = _a.fieldName,\n    options = __rest$1(_a, [\"className\", \"fieldName\"]);\n  var _b = usePayPalCardFields(),\n    cardFieldsForm = _b.cardFieldsForm,\n    registerField = _b.registerField,\n    unregisterField = _b.unregisterField;\n  var containerRef = useRef(null);\n  // Set errors is state so that they can be caught by React's error boundary\n  var _c = useState(null),\n    setError = _c[1];\n  function closeComponent() {\n    unregisterField(fieldName);\n  }\n  useEffect(function () {\n    if (!cardFieldsForm) {\n      setError(function () {\n        throw new Error(CARD_FIELDS_CONTEXT_ERROR);\n      });\n      return closeComponent;\n    }\n    if (!containerRef.current) {\n      return closeComponent;\n    }\n    var registeredField = registerField(fieldName, options, cardFieldsForm);\n    registeredField === null || registeredField === void 0 ? void 0 : registeredField.render(containerRef.current).catch(function (err) {\n      if (!hasChildren(containerRef)) {\n        // Component no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // Component is still in the DOM\n      setError(function () {\n        throw new Error(\"Failed to render <PayPal\".concat(fieldName, \" /> component. \").concat(err));\n      });\n    });\n    return closeComponent;\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", {\n    ref: containerRef,\n    className: className\n  });\n};\nvar PayPalNameField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"NameField\"\n  }, options));\n};\nvar PayPalNumberField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"NumberField\"\n  }, options));\n};\nvar PayPalExpiryField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"ExpiryField\"\n  }, options));\n};\nvar PayPalCVVField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"CVVField\"\n  }, options));\n};\nvar FlexContainer = function (_a) {\n  var children = _a.children;\n  return React.createElement(\"div\", {\n    style: {\n      display: \"flex\",\n      width: \"100%\"\n    }\n  }, children);\n};\n\n/**\nThis `<PayPalCardFieldsForm />` component renders the 4 individual fields for [Card Fields](https://developer.paypal.com/docs/business/checkout/advanced-card-payments/integrate#3-add-javascript-sdk-and-card-form) integrations.\nThis setup relies on the `<PayPalCardFieldsProvider />` parent component, which manages the state related to loading the JS SDK script and performs certain validations before rendering the fields.\n\n\n\nNote: If you want to have more granular control over the layout of how the fields are rendered, you can alternatively use our Individual Fields.\n*/\nvar PayPalCardFieldsForm = function (_a) {\n  var className = _a.className;\n  return React.createElement(\"div\", {\n    className: className\n  }, React.createElement(PayPalCardField, {\n    fieldName: \"NameField\"\n  }), React.createElement(PayPalCardField, {\n    fieldName: \"NumberField\"\n  }), React.createElement(FlexContainer, null, React.createElement(FullWidthContainer, null, React.createElement(PayPalCardField, {\n    fieldName: \"ExpiryField\"\n  })), React.createElement(FullWidthContainer, null, React.createElement(PayPalCardField, {\n    fieldName: \"CVVField\"\n  }))));\n};\nvar FUNDING$1 = {\n  PAYPAL: \"paypal\",\n  VENMO: \"venmo\",\n  APPLEPAY: \"applepay\",\n  ITAU: \"itau\",\n  CREDIT: \"credit\",\n  PAYLATER: \"paylater\",\n  CARD: \"card\",\n  IDEAL: \"ideal\",\n  SEPA: \"sepa\",\n  BANCONTACT: \"bancontact\",\n  GIROPAY: \"giropay\",\n  SOFORT: \"sofort\",\n  EPS: \"eps\",\n  MYBANK: \"mybank\",\n  P24: \"p24\",\n  PAYU: \"payu\",\n  BLIK: \"blik\",\n  TRUSTLY: \"trustly\",\n  OXXO: \"oxxo\",\n  BOLETO: \"boleto\",\n  BOLETOBANCARIO: \"boletobancario\",\n  WECHATPAY: \"wechatpay\",\n  MERCADOPAGO: \"mercadopago\",\n  MULTIBANCO: \"multibanco\",\n  SATISPAY: \"satispay\",\n  PAIDY: \"paidy\",\n  ZIMPLER: \"zimpler\",\n  MAXIMA: \"maxima\"\n};\n[FUNDING$1.IDEAL, FUNDING$1.BANCONTACT, FUNDING$1.GIROPAY, FUNDING$1.SOFORT, FUNDING$1.EPS, FUNDING$1.MYBANK, FUNDING$1.P24, FUNDING$1.PAYU, FUNDING$1.BLIK, FUNDING$1.TRUSTLY, FUNDING$1.OXXO, FUNDING$1.BOLETO, FUNDING$1.BOLETOBANCARIO, FUNDING$1.WECHATPAY, FUNDING$1.MERCADOPAGO, FUNDING$1.MULTIBANCO, FUNDING$1.SATISPAY, FUNDING$1.PAIDY, FUNDING$1.MAXIMA, FUNDING$1.ZIMPLER];\n\n// We do not re-export `FUNDING` from the `sdk-constants` module\n// directly because it has no type definitions.\n//\n// See https://github.com/paypal/react-paypal-js/issues/125\nvar FUNDING = FUNDING$1;\nexport { BraintreePayPalButtons, DISPATCH_ACTION, FUNDING, PAYPAL_HOSTED_FIELDS_TYPES, PayPalButtons, PayPalCVVField, PayPalCardFieldsContext, PayPalCardFieldsForm, PayPalCardFieldsProvider, PayPalExpiryField, PayPalHostedField, PayPalHostedFieldsProvider, PayPalMarks, PayPalMessages, PayPalNameField, PayPalNumberField, PayPalScriptProvider, SCRIPT_LOADING_STATE, ScriptContext, destroySDKScript, getScriptID, scriptReducer, usePayPalCardFields, usePayPalHostedFields, usePayPalScriptReducer, useScriptProviderContext };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;CAeC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACD;;AAEA;;;;CAIC,GACD,IAAI;AACJ,CAAC,SAAU,oBAAoB;IAC7B,oBAAoB,CAAC,UAAU,GAAG;IAClC,oBAAoB,CAAC,UAAU,GAAG;IAClC,oBAAoB,CAAC,WAAW,GAAG;IACnC,oBAAoB,CAAC,WAAW,GAAG;AACrC,CAAC,EAAE,wBAAwB,CAAC,uBAAuB,CAAC,CAAC;AACrD;;;;CAIC,GACD,IAAI;AACJ,CAAC,SAAU,eAAe;IACxB,eAAe,CAAC,iBAAiB,GAAG;IACpC,eAAe,CAAC,gBAAgB,GAAG;IACnC,eAAe,CAAC,yBAAyB,GAAG;AAC9C,CAAC,EAAE,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;AAC3C;;;;CAIC,GACD,IAAI;AACJ,CAAC,SAAU,0BAA0B;IACnC,0BAA0B,CAAC,SAAS,GAAG;IACvC,0BAA0B,CAAC,MAAM,GAAG;IACpC,0BAA0B,CAAC,kBAAkB,GAAG;IAChD,0BAA0B,CAAC,mBAAmB,GAAG;IACjD,0BAA0B,CAAC,kBAAkB,GAAG;IAChD,0BAA0B,CAAC,cAAc,GAAG;AAC9C,CAAC,EAAE,8BAA8B,CAAC,6BAA6B,CAAC,CAAC;AACjE,IAAI,WAAW;IACb,WAAW,OAAO,MAAM,IAAI,SAAS,SAAS,CAAC;QAC7C,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YACnD,IAAI,SAAS,CAAC,EAAE;YAChB,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAC9E;QACA,OAAO;IACT;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAC9B;AACA,SAAS,SAAS,CAAC,EAAE,CAAC;IACpB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAChG,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YAAY,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QAC3I,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnG;IACA,OAAO;AACT;AACA,SAAS,cAAc,EAAE,EAAE,IAAI,EAAE,IAAI;IACnC,IAAI,QAAQ,UAAU,MAAM,KAAK,GAAG,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,GAAG,IAAK;QACnF,IAAI,MAAM,CAAC,CAAC,KAAK,IAAI,GAAG;YACtB,IAAI,CAAC,IAAI,KAAK,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG;YAClD,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QACjB;IACF;IACA,OAAO,GAAG,MAAM,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACpD;AACA,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,KAAK,EAAE,UAAU,EAAE,OAAO;IAC5F,IAAI,IAAI,IAAI,MAAM;IAClB,OAAO,EAAE,IAAI,GAAG,mBAAmB,EAAE,KAAK,GAAG,OAAO,EAAE,UAAU,GAAG,YAAY;AACjF;AAEA;;6CAE6C,GAC7C,iDAAiD;AACjD,IAAI,YAAY;AAChB,IAAI,eAAe;IACjB,mBAAmB;IACnB,qBAAqB;IACrB,oBAAoB;IACpB,gBAAgB;IAChB,6BAA6B;IAC7B,oBAAoB;AACtB;AACA,IAAI,oBAAoB;AACxB;;4BAE4B,GAC5B,IAAI,8CAA8C;AAClD,IAAI,mBAAmB;AACvB,IAAI,mBAAmB,uCAAuC,MAAM,CAAC,kBAAkB;AACvF,IAAI,mCAAmC,uCAAuC,MAAM,CAAC,kBAAkB;AACvG;;qBAEqB,GACrB,IAAI,2BAA2B;AAC/B,IAAI,8BAA8B;AAClC;;iBAEiB,GACjB,IAAI,+BAA+B;AACnC,IAAI,yCAAyC;AAC7C;;mBAEmB,GACnB,IAAI,gCAAgC;AACpC,IAAI,uCAAuC;AAC3C,IAAI,4BAA4B;AAEhC;;;;;;;CAOC,GACD,SAAS,2BAA2B,SAAS;IAC3C,IAAI,cAAc,KAAK,GAAG;QACxB,YAAY;IACd;IACA,8DAA8D;IAC9D,OAAO,MAAM,CAAC,UAAU;AAC1B;AACA;;;;;;;CAOC,GACD,SAAS,4BAA4B,SAAS;IAC5C,IAAI,cAAc,KAAK,GAAG;QACxB,YAAY;IACd;IACA,8DAA8D;IAC9D,OAAO,MAAM,CAAC,UAAU;AAC1B;AACA;;;;;CAKC,GACD,SAAS,QAAQ,GAAG;IAClB,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK;QACnC,IAAI,GAAG,CAAC,IAAI,EAAE,EAAE;YACd,SAAS,GAAG,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;QAC5C;QACA,QAAQ,OAAO,YAAY,CAAC,KAAK,KAAK,GAAG,CAAC,SAAS;IACrD;IACA,OAAO;AACT;AACA,SAAS,qBAAqB,EAAE;IAC9B,IAAI,qBAAqB,GAAG,kBAAkB,EAC5C,kBAAkB,GAAG,eAAe,EACpC,KAAK,GAAG,sBAAsB,EAC9B,yBAAyB,OAAO,KAAK,IAAI,KAAK,IAC9C,KAAK,GAAG,gBAAgB,EACxB,mBAAmB,OAAO,KAAK,IAAI,2BAA2B;IAChE,IAAI,4BAA4B,gBAAgB,MAAM,CAAC,GAAG,WAAW,GAAG,MAAM,CAAC,gBAAgB,SAAS,CAAC;IACzG,IAAI,eAAe,qBAAqB,MAAM,CAAC,oBAAoB,uBAAuB,MAAM,CAAC,kBAAkB,KAAK,MAAM,CAAC,2BAA2B;IAC1J,0DAA0D;IAC1D,wGAAwG;IACxG,IAAI,sBAAsB,OAAO,2BAA2B,WAAW,yBAAyB,uBAAuB,IAAI,CAAC;IAC5H,IAAI,CAAC,oBAAoB,QAAQ,CAAC,kBAAkB;QAClD,IAAI,qBAAqB;YAAC;YAAqB;SAAgB,CAAC,MAAM,CAAC,SAAS,IAAI;QACpF,gBAAgB,4BAA4B,MAAM,CAAC,iBAAiB,4EAA4E,oDAAoD,MAAM,CAAC,oBAAoB;IACjO;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,YAAY,OAAO;IAC1B,sEAAsE;IACtE,IAAI,KAAK,SACP,KAAK;IACP,EAAE,CAAC,GAAG;IACN,IAAI,sBAAsB,SAAS,IAAI;QAAC,KAAK;KAAG;IAChD,OAAO,mBAAmB,MAAM,CAAC,QAAQ,KAAK,SAAS,CAAC;AAC1D;AACA;;;;CAIC,GACD,SAAS,iBAAiB,mBAAmB;IAC3C,IAAI,aAAa,KAAK,QAAQ,CAAC,aAAa,CAAC,UAAU,MAAM,CAAC,WAAW,OAAO,MAAM,CAAC,qBAAqB;IAC5G,IAAI,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,WAAW,UAAU,EAAE;QACjF,WAAW,UAAU,CAAC,WAAW,CAAC;IACpC;AACF;AACA;;;;;;CAMC,GACD,SAAS,cAAc,KAAK,EAAE,MAAM;IAClC,IAAI,IAAI;IACR,OAAQ,OAAO,IAAI;QACjB,KAAK,gBAAgB,cAAc;YACjC,IAAI,OAAO,OAAO,KAAK,KAAK,UAAU;gBACpC,OAAO,SAAS,SAAS,CAAC,GAAG,QAAQ;oBACnC,eAAe,OAAO,KAAK,CAAC,KAAK;oBACjC,2BAA2B,OAAO,KAAK,CAAC,OAAO;gBACjD;YACF;YACA,OAAO,SAAS,SAAS,CAAC,GAAG,QAAQ;gBACnC,eAAe,OAAO,KAAK;YAC7B;QACF,KAAK,gBAAgB,aAAa;YAChC,uEAAuE;YACvE,iBAAiB,MAAM,OAAO,CAAC,UAAU;YACzC,OAAO,SAAS,SAAS,CAAC,GAAG,QAAQ;gBACnC,eAAe,qBAAqB,OAAO;gBAC3C,SAAS,SAAS,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,aAAa,2BAA2B,CAAC,GAAG,aAAa,kBAAkB,EAAE,EAAE,GAAG,OAAO,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,GAAG,GAAG,MAAM,CAAC,YAAY,OAAO,KAAK,IAAI,EAAE;YAC7M;QACF,KAAK,gBAAgB,sBAAsB;YACzC,OAAO,SAAS,SAAS,CAAC,GAAG,QAAQ;gBACnC,iCAAiC,OAAO,KAAK;YAC/C;QACF;YACE;gBACE,OAAO;YACT;IACJ;AACF;AACA,mEAAmE;AACnE,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;AAElC;;;;;CAKC,GACD,SAAS,gBAAgB,aAAa;IACpC,IAAI,OAAO,CAAC,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,KAAK,IAAI,cAAc,QAAQ,MAAM,cAAc,cAAc,QAAQ,CAAC,MAAM,KAAK,GAAG;QACvJ,OAAO;IACT;IACA,MAAM,IAAI,MAAM;AAClB;AACA;;;;;;;;;CASC,GACD,IAAI,qCAAqC,SAAU,aAAa;IAC9D,IAAI,IAAI;IACR,IAAI,CAAC,CAAC,CAAC,KAAK,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,KAAK,IAAI,cAAc,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,aAAa,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,KAAK,IAAI,cAAc,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,aAAa,kBAAkB,CAAC,GAAG;QACnV,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS;IACP,IAAI,gBAAgB,gBAAgB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC/C,IAAI,uBAAuB,SAAS,SAAS,CAAC,GAAG,gBAAgB;QAC/D,WAAW,cAAc,aAAa,KAAK,qBAAqB,OAAO;QACvE,WAAW,cAAc,aAAa,KAAK,qBAAqB,OAAO;QACvE,YAAY,cAAc,aAAa,KAAK,qBAAqB,QAAQ;QACzE,YAAY,cAAc,aAAa,KAAK,qBAAqB,QAAQ;IAC3E;IACA,OAAO;QAAC;QAAsB,cAAc,QAAQ;KAAC;AACvD;AACA;;;;CAIC,GACD,SAAS;IACP,IAAI,gBAAgB,mCAAmC,gBAAgB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAClF,OAAO;QAAC;QAAe,cAAc,QAAQ;KAAC;AAChD;AAEA,uEAAuE;AACvE,IAAI,4BAA4B,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE,CAAC;AAE/C;;;;;;CAMC,GACD,SAAS;IACP,OAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AACpB;AACA,SAAS,cAAc,KAAK;IAC1B,IAAI,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,IAAI,MAAM,CAAC,GAAG;QAClC,GAAG;8CAAE,SAAU,MAAM,EAAE,IAAI,EAAE,QAAQ;gBACnC;;;;;;;SAOG,GACH,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,YAAY;oBACtC;0DAAO;4BACL,IAAI,OAAO,EAAE;4BACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;gCAC5C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;4BAC1B;4BACA,wDAAwD;4BACxD,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ;wBACpC;;gBACF;gBACA,OAAO,QAAQ,GAAG,CAAC,QAAQ,MAAM;YACnC;;IACF;IACA,SAAS,OAAO,GAAG,OAAO,MAAM,CAAC,SAAS,OAAO,EAAE;IACnD,OAAO,SAAS,OAAO;AACzB;AAEA;;;AAGA,GACA,IAAI,gBAAgB,SAAU,EAAE;IAC9B,IAAI;IACJ,IAAI,KAAK,GAAG,SAAS,EACnB,YAAY,OAAO,KAAK,IAAI,KAAK,IACjC,KAAK,GAAG,QAAQ,EAChB,WAAW,OAAO,KAAK,IAAI,QAAQ,IACnC,WAAW,GAAG,QAAQ,EACtB,KAAK,GAAG,aAAa,EACrB,gBAAgB,OAAO,KAAK,IAAI,EAAE,GAAG,IACrC,cAAc,SAAS,IAAI;QAAC;QAAa;QAAY;QAAY;KAAgB;IACnF,IAAI,kBAAkB,WAAW;QAC/B,SAAS;IACX,IAAI,CAAC;IACL,IAAI,aAAa,GAAG,MAAM,CAAC,WAAW,KAAK,MAAM,CAAC,WAAW,4BAA4B,IAAI,IAAI;IACjG,IAAI,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACjC,IAAI,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACrB,IAAI,aAAa,cAAc;IAC/B,IAAI,KAAK,wBAAwB,CAAC,EAAE,EAClC,aAAa,GAAG,UAAU,EAC1B,UAAU,GAAG,OAAO;IACtB,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAChB,cAAc,EAAE,CAAC,EAAE,EACnB,iBAAiB,EAAE,CAAC,EAAE;IACxB,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAChB,aAAa,EAAE,CAAC,EAAE,EAClB,gBAAgB,EAAE,CAAC,EAAE;IACvB,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAChB,gBAAgB,EAAE,CAAC,EAAE;IACvB,SAAS;QACP,IAAI,QAAQ,OAAO,KAAK,MAAM;YAC5B,QAAQ,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;YAC5B,2CAA2C;YAC7C;QACF;IACF;IACA,IAAI,CAAC,KAAK,QAAQ,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW,EAAE;QAC9E,QAAQ,OAAO,CAAC,WAAW,CAAC;YAC1B,SAAS,YAAY,OAAO;QAC9B;IACF;IACA,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,gDAAgD;YAChD,IAAI,eAAe,OAAO;gBACxB,OAAO;YACT;YACA,IAAI,wBAAwB,2BAA2B,QAAQ,aAAa;YAC5E,qCAAqC;YACrC,IAAI,0BAA0B,aAAa,sBAAsB,OAAO,KAAK,WAAW;gBACtF;+CAAc;wBACZ,MAAM,IAAI,MAAM,qBAAqB;4BACnC,oBAAoB,cAAc,WAAW;4BAC7C,iBAAiB;4BACjB,wBAAwB,QAAQ,UAAU;4BAC1C,kBAAkB,OAAO,CAAC,aAAa,cAAc,CAAC;wBACxD;oBACF;;gBACA,OAAO;YACT;YACA,IAAI;2DAAkB,SAAU,IAAI,EAAE,OAAO;oBAC3C,eAAe;oBACf,IAAI,OAAO,YAAY,MAAM,KAAK,YAAY;wBAC5C,YAAY,MAAM,CAAC,MAAM;oBAC3B;gBACF;;YACA,IAAI;gBACF,QAAQ,OAAO,GAAG,sBAAsB,OAAO,CAAC,SAAS,SAAS,CAAC,GAAG,aAAa;oBACjF,QAAQ;gBACV;YACF,EAAE,OAAO,KAAK;gBACZ,OAAO;+CAAc;wBACnB,MAAM,IAAI,MAAM,wEAAwE,MAAM,CAAC;oBACjG;;YACF;YACA,uCAAuC;YACvC,IAAI,QAAQ,OAAO,CAAC,UAAU,OAAO,OAAO;gBAC1C,cAAc;gBACd,OAAO;YACT;YACA,IAAI,CAAC,oBAAoB,OAAO,EAAE;gBAChC,OAAO;YACT;YACA,QAAQ,OAAO,CAAC,MAAM,CAAC,oBAAoB,OAAO,EAAE,KAAK;2CAAC,SAAU,GAAG;oBACrE,2EAA2E;oBAC3E,IAAI,oBAAoB,OAAO,KAAK,QAAQ,oBAAoB,OAAO,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG;wBAC7F,mFAAmF;wBACnF;oBACF;oBACA,+CAA+C;oBAC/C;mDAAc;4BACZ,MAAM,IAAI,MAAM,iDAAiD,MAAM,CAAC;wBAC1E;;gBACF;;YACA,OAAO;QACP,uDAAuD;QACzD;kCAAG,cAAc,cAAc;QAAC;KAAW,EAAE,eAAe,OAAO;QAAC,YAAY,aAAa;KAAC,EAAE;IAChG,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,gBAAgB,MAAM;gBACxB;YACF;YACA,IAAI,aAAa,MAAM;gBACrB,YAAY,OAAO,GAAG,KAAK;+CAAC;oBAC1B,6CAA6C;oBAC/C;;YACF,OAAO;gBACL,YAAY,MAAM,GAAG,KAAK;+CAAC;oBACzB,4CAA4C;oBAC9C;;YACF;QACF;kCAAG;QAAC;QAAU;KAAY;IAC1B,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACvF,KAAK;QACL,OAAO;QACP,WAAW;IACb,KAAK;AACP;AACA,cAAc,WAAW,GAAG;AAC5B,SAAS,OAAO,CAAC,EAAE,CAAC;IAClB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAChG,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YAAY,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QAC3I,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnG;IACA,OAAO;AACT;AACA,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,KAAK,EAAE,UAAU,EAAE,OAAO;IAC5F,IAAI,IAAI,IAAI,MAAM;IAClB,OAAO,EAAE,IAAI,GAAG,mBAAmB,EAAE,KAAK,GAAG,OAAO,EAAE,UAAU,GAAG,YAAY;AACjF;AACA,SAAS,WAAW,GAAG,EAAE,UAAU;IACjC,IAAI,gBAAgB,SAAS,aAAa,CAAC,gBAAgB,MAAM,CAAC,KAAK;IACvE,IAAI,kBAAkB,MAAM,OAAO;IACnC,IAAI,aAAa,oBAAoB,KAAK;IAC1C,IAAI,qBAAqB,cAAc,SAAS;IAChD,OAAO,mBAAmB,OAAO,CAAC,OAAO;IACzC,IAAI,OAAO,IAAI,CAAC,mBAAmB,OAAO,EAAE,MAAM,KAAK,OAAO,IAAI,CAAC,WAAW,OAAO,EAAE,MAAM,EAAE;QAC7F,OAAO;IACT;IACA,IAAI,eAAe;IACnB,OAAO,IAAI,CAAC,mBAAmB,OAAO,EAAE,OAAO,CAAC,SAAU,GAAG;QAC3D,IAAI,mBAAmB,OAAO,CAAC,IAAI,KAAK,WAAW,OAAO,CAAC,IAAI,EAAE;YAC/D,eAAe;QACjB;IACF;IACA,OAAO,eAAe,gBAAgB;AACxC;AACA,SAAS,oBAAoB,EAAE;IAC7B,IAAI,MAAM,GAAG,GAAG,EACd,aAAa,GAAG,UAAU,EAC1B,YAAY,GAAG,SAAS,EACxB,UAAU,GAAG,OAAO;IACtB,IAAI,YAAY,oBAAoB,KAAK;IACzC,UAAU,OAAO,GAAG;IACpB,UAAU,MAAM,GAAG;IACnB,SAAS,IAAI,CAAC,YAAY,CAAC,WAAW,SAAS,IAAI,CAAC,iBAAiB;AACvE;AACA,SAAS,eAAe,EAAE;IACxB,IAAI,mBAAmB,GAAG,UAAU,EAClC,cAAc,GAAG,WAAW,EAC5B,UAAU,OAAO,IAAI;QAAC;QAAc;KAAc;IACpD,IAAI,aAAa,oBAAoB,kBAAkB;IACvD,IAAI,yBAAyB;IAC7B,IAAI,KAAK,OAAO,IAAI,CAAC,wBAAwB,MAAM,CAAC,SAAU,GAAG;QAC7D,OAAO,OAAO,sBAAsB,CAAC,IAAI,KAAK,eAAe,sBAAsB,CAAC,IAAI,KAAK,QAAQ,sBAAsB,CAAC,IAAI,KAAK;IACvI,GAAG,MAAM,CAAC,SAAU,WAAW,EAAE,GAAG;QAClC,IAAI,QAAQ,sBAAsB,CAAC,IAAI,CAAC,QAAQ;QAChD,MAAM,qBAAqB;QAC3B,IAAI,IAAI,SAAS,CAAC,GAAG,OAAO,UAAU,QAAQ,eAAe;YAC3D,YAAY,UAAU,CAAC,IAAI,GAAG;QAChC,OAAO;YACL,YAAY,WAAW,CAAC,IAAI,GAAG;QACjC;QACA,OAAO;IACT,GAAG;QACD,aAAa,CAAC;QACd,YAAY,CAAC;IACf,IACA,cAAc,GAAG,WAAW,EAC5B,aAAa,GAAG,UAAU;IAC5B,IAAI,WAAW,CAAC,cAAc,IAAI,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG;QAChF,UAAU,CAAC,mBAAmB,GAAG,WAAW,CAAC,cAAc;QAC3D,WAAW,CAAC,cAAc,GAAG;IAC/B;IACA,OAAO;QACL,KAAK,GAAG,MAAM,CAAC,YAAY,KAAK,MAAM,CAAC,oBAAoB;QAC3D,YAAY;IACd;AACF;AACA,SAAS,qBAAqB,GAAG;IAC/B,IAAI,WAAW,SAAU,KAAK,EAAE,YAAY;QAC1C,OAAO,CAAC,eAAe,MAAM,EAAE,IAAI,MAAM,WAAW;IACtD;IACA,OAAO,IAAI,OAAO,CAAC,0BAA0B;AAC/C;AACA,SAAS,oBAAoB,MAAM;IACjC,IAAI,cAAc;IAClB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,SAAU,GAAG;QACvC,IAAI,YAAY,MAAM,KAAK,GAAG,eAAe;QAC7C,eAAe,MAAM,MAAM,MAAM,CAAC,IAAI;IACxC;IACA,OAAO;AACT;AACA,SAAS,kBAAkB,WAAW;IACpC,OAAO,gBAAgB,YAAY,0CAA0C;AAC/E;AACA,SAAS,oBAAoB,GAAG,EAAE,UAAU;IAC1C,IAAI,eAAe,KAAK,GAAG;QACzB,aAAa,CAAC;IAChB;IACA,IAAI,YAAY,SAAS,aAAa,CAAC;IACvC,UAAU,GAAG,GAAG;IAChB,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,SAAU,GAAG;QAC3C,UAAU,YAAY,CAAC,KAAK,UAAU,CAAC,IAAI;QAC3C,IAAI,QAAQ,kBAAkB;YAC5B,UAAU,YAAY,CAAC,SAAS,UAAU,CAAC,iBAAiB;QAC9D;IACF;IACA,OAAO;AACT;AACA,SAAS,WAAW,OAAO,EAAE,eAAe;IAC1C,IAAI,oBAAoB,KAAK,GAAG;QAC9B,kBAAkB;IACpB;IACA,kBAAkB,SAAS;IAC3B,IAAI,OAAO,aAAa,aAAa,OAAO,gBAAgB,OAAO,CAAC;IACpE,IAAI,KAAK,eAAe,UACtB,MAAM,GAAG,GAAG,EACZ,aAAa,GAAG,UAAU;IAC5B,IAAI,YAAY,UAAU,CAAC,iBAAiB,IAAI;IAChD,IAAI,0BAA0B,yBAAyB;IACvD,IAAI,CAAC,UAAU,CAAC,sBAAsB,EAAE;QACtC,UAAU,CAAC,sBAAsB,GAAG;IACtC;IACA,IAAI,WAAW,KAAK,eAAe,yBAAyB;QAC1D,OAAO,gBAAgB,OAAO,CAAC;IACjC;IACA,OAAO,iBAAiB;QACtB,KAAK;QACL,YAAY;IACd,GAAG,iBAAiB,IAAI,CAAC;QACvB,IAAI,qBAAqB,yBAAyB;QAClD,IAAI,oBAAoB;YACtB,OAAO;QACT;QACA,MAAM,IAAI,MAAM,cAAc,MAAM,CAAC,WAAW;IAClD;AACF;AACA,SAAS,iBAAiB,OAAO,EAAE,eAAe;IAChD,IAAI,oBAAoB,KAAK,GAAG;QAC9B,kBAAkB;IACpB;IACA,kBAAkB,SAAS;IAC3B,IAAI,MAAM,QAAQ,GAAG,EACnB,aAAa,QAAQ,UAAU;IACjC,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,KAAK,GAAG;QAC/C,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,OAAO,eAAe,eAAe,OAAO,eAAe,UAAU;QACvE,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,IAAI,gBAAgB,SAAU,OAAO,EAAE,MAAM;QAClD,IAAI,OAAO,aAAa,aAAa,OAAO;QAC5C,oBAAoB;YAClB,KAAK;YACL,YAAY;YACZ,WAAW;gBACT,OAAO;YACT;YACA,SAAS;gBACP,IAAI,eAAe,IAAI,MAAM,gBAAgB,MAAM,CAAC,KAAK;gBACzD,OAAO,OAAO;YAChB;QACF;IACF;AACF;AACA,SAAS,yBAAyB,SAAS;IACzC,OAAO,MAAM,CAAC,UAAU;AAC1B;AACA,SAAS,kBAAkB,OAAO,EAAE,eAAe;IACjD,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,cAAc,QAAQ,WAAW;IACrC,IAAI,eAAe,gBAAgB,gBAAgB,gBAAgB,WAAW;QAC5E,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,OAAO,oBAAoB,eAAe,OAAO,oBAAoB,YAAY;QACnF,MAAM,IAAI,MAAM;IAClB;AACF;AAEA;;;;;CAKC,GACD,IAAI,4BAA4B,SAAU,eAAe;IACvD,IAAI,IAAI;IACR,IAAI,OAAO,CAAC,CAAC,KAAK,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM,cAAc,OAAO,CAAC,CAAC,KAAK,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM,YAAY;QAC5V,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AACA;;;;;;CAMC,GACD,IAAI,kBAAkB,SAAU,WAAW,EAAE,sBAAsB;IACjE,IAAI,iBAAiB,YAAY,WAAW;IAC5C,IAAI,4BAA4B,YAAY,sBAAsB;IAClE,IAAI,eAAe,YAAY,SAAS;IACxC,IAAI,OAAO,mBAAmB,YAAY;QACxC,YAAY,WAAW,GAAG,SAAU,IAAI,EAAE,OAAO;YAC/C,OAAO,eAAe,MAAM,SAAS,SAAS,CAAC,GAAG,UAAU;gBAC1D,WAAW;YACb;QACF;IACF;IACA,IAAI,OAAO,8BAA8B,YAAY;QACnD,YAAY,sBAAsB,GAAG,SAAU,IAAI,EAAE,OAAO;YAC1D,OAAO,0BAA0B,MAAM,SAAS,SAAS,CAAC,GAAG,UAAU;gBACrE,WAAW;YACb;QACF;IACF;IACA,IAAI,OAAO,iBAAiB,YAAY;QACtC,YAAY,SAAS,GAAG,SAAU,IAAI,EAAE,OAAO;YAC7C,OAAO,aAAa,MAAM,SAAS,SAAS,CAAC,GAAG,UAAU;gBACxD,WAAW;YACb;QACF;IACF;IACA,OAAO,SAAS,CAAC,GAAG;AACtB;AACA;;;;;;;;;;;;;;;CAeC,GACD,IAAI,wBAAwB,SAAU,eAAe;IACnD,IAAI,mBAAmB,0BAA0B,kBAAkB;QACjE,OAAO,QAAQ,OAAO,CAAC;IACzB;IACA,OAAO,QAAQ,GAAG,CAAC;QAAC,iBAAiB;YACnC,KAAK;QACP;QAAI,iBAAiB;YACnB,KAAK;QACP;KAAG,EAAE,IAAI,CAAC;QACR,OAAO;IACT;AACF;AAEA;;;;;;;;AAQA,GACA,IAAI,yBAAyB,SAAU,EAAE;IACvC,IAAI,KAAK,GAAG,SAAS,EACnB,YAAY,OAAO,KAAK,IAAI,KAAK,IACjC,KAAK,GAAG,QAAQ,EAChB,WAAW,OAAO,KAAK,IAAI,QAAQ,IACnC,WAAW,GAAG,QAAQ,EACtB,KAAK,GAAG,aAAa,EACrB,gBAAgB,OAAO,KAAK,IAAI,EAAE,GAAG,IACrC,qBAAqB,GAAG,kBAAkB,EAC1C,oBAAoB,GAAG,iBAAiB,EACxC,cAAc,SAAS,IAAI;QAAC;QAAa;QAAY;QAAY;QAAiB;QAAsB;KAAoB;IAC9H,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAChB,gBAAgB,EAAE,CAAC,EAAE;IACvB,IAAI,KAAK,4BACP,kBAAkB,EAAE,CAAC,EAAE,EACvB,WAAW,EAAE,CAAC,EAAE;IAClB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,sBAAsB,oBAAoB,IAAI;oDAAC,SAAU,SAAS;oBAChE,IAAI,wBAAwB,gBAAgB,OAAO,CAAC,aAAa,kBAAkB,CAAC;oBACpF,IAAI,cAAc,gBAAgB,OAAO,CAAC,aAAa,iBAAiB,CAAC;oBACzE,OAAO,UAAU,MAAM,CAAC,MAAM,CAAC;wBAC7B,eAAe,yBAAyB;oBAC1C,GAAG,IAAI;4DAAC,SAAU,cAAc;4BAC9B,IAAI,eAAe,oBAAoB;gCACrC,mBAAmB;4BACrB,IAAI,CAAC;4BACL,OAAO,UAAU,cAAc,CAAC,MAAM,CAAC,SAAS,SAAS,CAAC,GAAG,eAAe;gCAC1E,QAAQ;4BACV;wBACF;2DAAG,IAAI;4DAAC,SAAU,sBAAsB;4BACtC,SAAS;gCACP,MAAM,gBAAgB,sBAAsB;gCAC5C,OAAO;4BACT;wBACF;;gBACF;mDAAG,KAAK;oDAAC,SAAU,GAAG;oBACpB;4DAAc;4BACZ,MAAM,IAAI,MAAM,GAAG,MAAM,CAAC,mBAAmB,KAAK,MAAM,CAAC;wBAC3D;;gBACF;;QACA,uDAAuD;QACzD;2CAAG;QAAC,gBAAgB,OAAO;KAAC;IAC5B,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,gBAAgB,+BAA+B,IAAI,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,eAAe,SAAS;QAC9I,WAAW;QACX,UAAU;QACV,eAAe;IACjB,GAAG,gBAAgB,aAAa,gBAAgB,+BAA+B,IAAI;AACrF;AAEA;;;;;;;AAOA,GACA,IAAI,cAAc,SAAU,EAAE;IAC5B,IAAI,KAAK,GAAG,SAAS,EACnB,YAAY,OAAO,KAAK,IAAI,KAAK,IACjC,WAAW,GAAG,QAAQ,EACtB,YAAY,SAAS,IAAI;QAAC;QAAa;KAAW;IACpD,IAAI,KAAK,wBAAwB,CAAC,EAAE,EAClC,aAAa,GAAG,UAAU,EAC1B,UAAU,GAAG,OAAO;IACtB,IAAI,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAChB,aAAa,EAAE,CAAC,EAAE,EAClB,gBAAgB,EAAE,CAAC,EAAE;IACvB,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAChB,gBAAgB,EAAE,CAAC,EAAE;IACvB;;GAEC,GACD,IAAI,mBAAmB,SAAU,IAAI;QACnC,IAAI,UAAU,iBAAiB,OAAO;QACtC,qCAAqC;QACrC,IAAI,CAAC,WAAW,CAAC,KAAK,UAAU,IAAI;YAClC,OAAO,cAAc;QACvB;QACA,6CAA6C;QAC7C,IAAI,QAAQ,UAAU,EAAE;YACtB,QAAQ,WAAW,CAAC,QAAQ,UAAU;QACxC;QACA,KAAK,MAAM,CAAC,SAAS,KAAK,CAAC,SAAU,GAAG;YACtC,2EAA2E;YAC3E,IAAI,YAAY,QAAQ,QAAQ,QAAQ,CAAC,MAAM,KAAK,GAAG;gBACrD,iFAAiF;gBACjF;YACF;YACA,6CAA6C;YAC7C,cAAc;gBACZ,MAAM,IAAI,MAAM,+CAA+C,MAAM,CAAC;YACxE;QACF;IACF;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,gDAAgD;YAChD,IAAI,eAAe,OAAO;gBACxB;YACF;YACA,IAAI,wBAAwB,2BAA2B,OAAO,CAAC,aAAa,cAAc,CAAC;YAC3F,qCAAqC;YACrC,IAAI,0BAA0B,aAAa,sBAAsB,KAAK,KAAK,WAAW;gBACpF,OAAO;6CAAc;wBACnB,MAAM,IAAI,MAAM,qBAAqB;4BACnC,oBAAoB,YAAY,WAAW;4BAC3C,iBAAiB;4BACjB,wBAAwB,QAAQ,UAAU;4BAC1C,kBAAkB,OAAO,CAAC,aAAa,cAAc,CAAC;wBACxD;oBACF;;YACF;YACA,iBAAiB,sBAAsB,KAAK,CAAC,SAAS,CAAC,GAAG;QAC1D,uDAAuD;QACzD;gCAAG;QAAC;QAAY,UAAU,aAAa;KAAC;IACxC,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACvF,KAAK;QACL,WAAW;IACb,KAAK;AACP;AACA,YAAY,WAAW,GAAG;AAE1B;;;AAGA,GACA,IAAI,iBAAiB,SAAU,EAAE;IAC/B,IAAI,KAAK,GAAG,SAAS,EACnB,YAAY,OAAO,KAAK,IAAI,KAAK,IACjC,KAAK,GAAG,aAAa,EACrB,gBAAgB,OAAO,KAAK,IAAI,EAAE,GAAG,IACrC,eAAe,SAAS,IAAI;QAAC;QAAa;KAAgB;IAC5D,IAAI,KAAK,wBAAwB,CAAC,EAAE,EAClC,aAAa,GAAG,UAAU,EAC1B,UAAU,GAAG,OAAO;IACtB,IAAI,uBAAuB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAClC,IAAI,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACtB,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAChB,gBAAgB,EAAE,CAAC,EAAE;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,gDAAgD;YAChD,IAAI,eAAe,OAAO;gBACxB;YACF;YACA,IAAI,wBAAwB,2BAA2B,OAAO,CAAC,aAAa,cAAc,CAAC;YAC3F,qCAAqC;YACrC,IAAI,0BAA0B,aAAa,sBAAsB,QAAQ,KAAK,WAAW;gBACvF,OAAO;gDAAc;wBACnB,MAAM,IAAI,MAAM,qBAAqB;4BACnC,oBAAoB,eAAe,WAAW;4BAC9C,iBAAiB;4BACjB,wBAAwB,QAAQ,UAAU;4BAC1C,kBAAkB,OAAO,CAAC,aAAa,cAAc,CAAC;wBACxD;oBACF;;YACF;YACA,SAAS,OAAO,GAAG,sBAAsB,QAAQ,CAAC,SAAS,CAAC,GAAG;YAC/D,SAAS,OAAO,CAAC,MAAM,CAAC,qBAAqB,OAAO,EAAE,KAAK;4CAAC,SAAU,GAAG;oBACvE,2EAA2E;oBAC3E,IAAI,qBAAqB,OAAO,KAAK,QAAQ,qBAAqB,OAAO,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG;wBAC/F,oFAAoF;wBACpF;oBACF;oBACA,gDAAgD;oBAChD;oDAAc;4BACZ,MAAM,IAAI,MAAM,kDAAkD,MAAM,CAAC;wBAC3E;;gBACF;;QACA,uDAAuD;QACzD;mCAAG,cAAc;QAAC;KAAW,EAAE,eAAe;IAC9C,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAChC,KAAK;QACL,WAAW;IACb;AACF;AACA,eAAe,WAAW,GAAG;AAE7B;;;;;CAKC,GACD,IAAI,uBAAuB,SAAU,EAAE;IACrC,IAAI;IACJ,IAAI,KAAK,GAAG,OAAO,EACjB,UAAU,OAAO,KAAK,IAAI;QACxB,UAAU;IACZ,IAAI,IACJ,WAAW,GAAG,QAAQ,EACtB,KAAK,GAAG,YAAY,EACpB,eAAe,OAAO,KAAK,IAAI,QAAQ;IACzC,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,eAAe;QAC/B,SAAS,SAAS,SAAS,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,aAAa,mBAAmB,CAAC,GAAG,aAAa,kBAAkB,EAAE,EAAE,CAAC,aAAa,2BAA2B,CAAC,GAAG,aAAa,kBAAkB,EAAE,EAAE,CAAC,UAAU,GAAG,GAAG,MAAM,CAAC,YAAY,WAAW,EAAE;QAC9P,eAAe,eAAe,qBAAqB,OAAO,GAAG,qBAAqB,OAAO;IAC3F,IACA,QAAQ,EAAE,CAAC,EAAE,EACb,WAAW,EAAE,CAAC,EAAE;IAClB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,iBAAiB,SAAS,MAAM,aAAa,KAAK,qBAAqB,OAAO,EAAE;gBAClF,OAAO,SAAS;oBACd,MAAM,gBAAgB,cAAc;oBACpC,OAAO,qBAAqB,OAAO;gBACrC;YACF;YACA,IAAI,MAAM,aAAa,KAAK,qBAAqB,OAAO,EAAE;gBACxD;YACF;YACA,IAAI,eAAe;YACnB,WAAW,MAAM,OAAO,EAAE,IAAI;kDAAC;oBAC7B,IAAI,cAAc;wBAChB,SAAS;4BACP,MAAM,gBAAgB,cAAc;4BACpC,OAAO,qBAAqB,QAAQ;wBACtC;oBACF;gBACF;iDAAG,KAAK;kDAAC,SAAU,GAAG;oBACpB,QAAQ,KAAK,CAAC,GAAG,MAAM,CAAC,mBAAmB,KAAK,MAAM,CAAC;oBACvD,IAAI,cAAc;wBAChB,SAAS;4BACP,MAAM,gBAAgB,cAAc;4BACpC,OAAO;gCACL,OAAO,qBAAqB,QAAQ;gCACpC,SAAS,OAAO;4BAClB;wBACF;oBACF;gBACF;;YACA;kDAAO;oBACL,eAAe;gBACjB;;QACF;yCAAG;QAAC,MAAM,OAAO;QAAE;QAAc,MAAM,aAAa;KAAC;IACrD,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,cAAc,QAAQ,EAAE;QACjD,OAAO,SAAS,SAAS,CAAC,GAAG,QAAQ;YACnC,UAAU;QACZ;IACF,GAAG;AACL;AAEA;;;;;;;CAOC,GACD,IAAI,0BAA0B,SAAU,YAAY;IAClD,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe,CAAC;IAClB;IACA,IAAI,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,IAAI,sBAAsB,SAAU,SAAS;QAC3C,iBAAiB,OAAO,GAAG,SAAS,SAAS,CAAC,GAAG,iBAAiB,OAAO,GAAG;IAC9E;IACA,OAAO;QAAC;QAAkB;KAAoB;AAChD;AAEA;;;;;;;;;CASC,GACD,IAAI,mCAAmC,SAAU,EAAE;IACjD,IAAI,KAAK,GAAG,UAAU,EACpB,aAAa,OAAO,KAAK,IAAI,KAAK,IAClC,KAAK,aAAa,cAAc,EAChC,KAAK,EAAE,CAAC,GAAG,EACX,gBAAgB,OAAO,KAAK,IAAI,2BAA2B;IAC7D,IAAI,qBAAqB,aAAa,GAAG,MAAM,CAAC,YAAY,oBAAoB;IAChF,IAAI,eAAe,kEAAkE,MAAM,CAAC,eAAe;IAC3G,IAAI,CAAC,WAAW,QAAQ,CAAC,kBAAkB;QACzC,gBAAgB,8JAA8J,MAAM,CAAC,oBAAoB;IAC3M;IACA,OAAO;AACT;AACA;;;;;;;CAOC,GACD,IAAI,yBAAyB,SAAU,aAAa;IAClD,OAAO,CAAC,cAAc,QAAQ,CAAC,2BAA2B,eAAe,KAAK,CAAC,cAAc,QAAQ,CAAC,2BAA2B,gBAAgB,KAAK,CAAC,cAAc,QAAQ,CAAC,2BAA2B,eAAe;AAC1N;AACA;;;;;;CAMC,GACD,IAAI,qBAAqB,SAAU,aAAa;IAC9C,IAAI,CAAC,cAAc,QAAQ,CAAC,2BAA2B,MAAM,KAAK,CAAC,cAAc,QAAQ,CAAC,2BAA2B,GAAG,KAAK,uBAAuB,gBAAgB;QAClK,MAAM,IAAI,MAAM;IAClB;AACF;AACA;;;;;CAKC,GACD,IAAI,sBAAsB,SAAU,aAAa;IAC/C,IAAI,cAAc,MAAM,KAAK,IAAI,IAAI,eAAe,IAAI,EAAE;QACxD,MAAM,IAAI,MAAM;IAClB;AACF;AACA;;;;;;;;;CASC,GACD,IAAI,8BAA8B,SAAU,gBAAgB;IAC1D,mBAAmB;IACnB,oBAAoB;AACtB;AAEA;;;;;;;;AAQA,GACA,IAAI,6BAA6B,SAAU,EAAE;IAC3C,IAAI,SAAS,GAAG,MAAM,EACpB,cAAc,GAAG,WAAW,EAC5B,mBAAmB,GAAG,gBAAgB,EACtC,WAAW,GAAG,QAAQ,EACtB,eAAe,GAAG,YAAY;IAChC,IAAI,KAAK,0BAA0B,CAAC,EAAE,EACpC,UAAU,GAAG,OAAO,EACpB,gBAAgB,GAAG,aAAa;IAClC,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAChB,aAAa,EAAE,CAAC,EAAE,EAClB,gBAAgB,EAAE,CAAC,EAAE;IACvB,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,KACd,aAAa,EAAE,CAAC,EAAE,EAClB,gBAAgB,EAAE,CAAC,EAAE;IACvB,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAChB,gBAAgB,EAAE,CAAC,EAAE;IACvB,IAAI,2BAA2B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACtC,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACxB,IAAI,KAAK,2BACP,mBAAmB,EAAE,CAAC,EAAE,EACxB,sBAAsB,EAAE,CAAC,EAAE;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR,IAAI;YACJ,4BAA4B,OAAO,IAAI,CAAC,iBAAiB,OAAO;YAChE,mFAAmF;YACnF,IAAI,CAAC,CAAC,kBAAkB,qBAAqB,QAAQ,GAAG;gBACtD;YACF;YACA,kEAAkE;YAClE,aAAa,OAAO,GAAG,2BAA2B,OAAO,CAAC,aAAa,cAAc,CAAC,EAAE,YAAY;YACpG,IAAI,CAAC,aAAa,OAAO,EAAE;gBACzB,MAAM,IAAI,MAAM,iCAAiC,CAAC,KAAK;oBACrD,YAAY,QAAQ,UAAU;gBAChC,GAAG,EAAE,CAAC,aAAa,cAAc,CAAC,GAAG,OAAO,CAAC,aAAa,cAAc,CAAC,EAAE,EAAE;YAC/E;YACA,IAAI,CAAC,aAAa,OAAO,CAAC,UAAU,IAAI;gBACtC,OAAO,cAAc;YACvB;YACA,2CAA2C;YAC3C,IAAI,YAAY;gBACd,WAAW,QAAQ;YACrB;YACA,aAAa,OAAO,CAAC,MAAM,CAAC;gBAC1B,6CAA6C;gBAC7C,aAAa;gBACb,QAAQ,iBAAiB,OAAO;gBAChC,cAAc;gBACd,QAAQ;YACV,GAAG,IAAI;wDAAC,SAAU,kBAAkB;oBAClC,IAAI,yBAAyB,OAAO,EAAE;wBACpC,cAAc;oBAChB;gBACF;uDAAG,KAAK;wDAAC,SAAU,GAAG;oBACpB;gEAAc;4BACZ,MAAM,IAAI,MAAM,8DAA8D,MAAM,CAAC;wBACvF;;gBACF;;QACF;+CAAG;QAAC;QAAe;KAAO,GAAG,kDAAkD;IAC/E,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAChC,KAAK;IACP,GAAG,aAAa,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,0BAA0B,QAAQ,EAAE;QACtE,OAAO;YACL,YAAY;YACZ,qBAAqB;QACvB;IACF,GAAG,YAAY;AACjB;AAEA;;;;;;;;;;;;;;;;AAgBA,GACA,IAAI,oBAAoB,SAAU,EAAE;IAClC,IAAI,kBAAkB,GAAG,eAAe,EACtC,wDAAwD;IACxD,UAAU,GAAG,OAAO,EACpB,wDAAwD;IACxD,QAAQ,SAAS,IAAI;QAAC;QAAmB;KAAU;IACrD,IAAI,qBAAqB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI;YACJ,IAAI,CAAC,CAAC,uBAAuB,QAAQ,uBAAuB,KAAK,IAAI,KAAK,IAAI,mBAAmB,mBAAmB,GAAG;gBACrH,MAAM,IAAI,MAAM;YAClB;YACA,kCAAkC;YAClC,mBAAmB,mBAAmB,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,gBAAgB,GAAG;gBACrE,UAAU,QAAQ,QAAQ;gBAC1B,aAAa,QAAQ,WAAW;gBAChC,MAAM,QAAQ,IAAI;gBAClB,aAAa,QAAQ,WAAW;gBAChC,WAAW,QAAQ,SAAS;gBAC5B,QAAQ,QAAQ,MAAM;gBACtB,WAAW,QAAQ,SAAS;gBAC5B,WAAW,QAAQ,SAAS;gBAC5B,SAAS,QAAQ,OAAO;gBACxB,wBAAwB,QAAQ,sBAAsB;YACxD,GAAG,EAAE;QACP;sCAAG,EAAE,GAAG,kDAAkD;IAC1D,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO,SAAS,CAAC,GAAG;AACjD;AAEA;;;;;;;;;CASC,GACD,IAAI,iCAAiC,SAAU,EAAE;IAC/C,IAAI,KAAK,GAAG,UAAU,EACpB,aAAa,OAAO,KAAK,IAAI,KAAK,IAClC,KAAK,aAAa,cAAc,EAChC,KAAK,EAAE,CAAC,GAAG,EACX,gBAAgB,OAAO,KAAK,IAAI,2BAA2B;IAC7D,IAAI,qBAAqB,aAAa,GAAG,MAAM,CAAC,YAAY,kBAAkB;IAC9E,IAAI,eAAe,gEAAgE,MAAM,CAAC,eAAe;IACzG,IAAI,CAAC,WAAW,QAAQ,CAAC,gBAAgB;QACvC,gBAAgB,4JAA4J,MAAM,CAAC,oBAAoB;IACzM;IACA,OAAO;AACT;AACA,SAAS;IACP;AACF;AACA,SAAS,YAAY,SAAS;IAC5B,IAAI;IACJ,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,UAAU,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,CAAC,MAAM;AAC5F;AACA,IAAI,0BAA0B,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;IAC1C,gBAAgB;IAChB,QAAQ,CAAC;IACT,eAAe;IACf,iBAAiB,OAAO,gEAAgE;AAC1F;AACA,IAAI,sBAAsB;IACxB,OAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AACpB;AACA,IAAI,8BAA8B;IAChC,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAChB,WAAW,EAAE,CAAC,EAAE;IAClB,IAAI,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,CAAC;IAC/B,IAAI,gBAAgB;QAClB,IAAI,QAAQ,EAAE;QACd,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC5C,KAAK,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;QAC3B;QACA,IAAI,YAAY,KAAK,CAAC,EAAE,EACtB,UAAU,KAAK,CAAC,EAAE,EAClB,aAAa,KAAK,CAAC,EAAE;QACvB,IAAI,iBAAiB,OAAO,CAAC,UAAU,EAAE;YACvC,SAAS;gBACP,MAAM,IAAI,MAAM;YAClB;QACF;QACA,iBAAiB,OAAO,CAAC,UAAU,GAAG,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,UAAU,CAAC;QACpH,OAAO,iBAAiB,OAAO,CAAC,UAAU;IAC5C;IACA,IAAI,kBAAkB,SAAU,SAAS;QACvC,IAAI,QAAQ,iBAAiB,OAAO,CAAC,UAAU;QAC/C,IAAI,OAAO;YACT,MAAM,KAAK,GAAG,KAAK,CAAC;YACpB,OAAO,iBAAiB,OAAO,CAAC,UAAU;QAC5C;IACF;IACA,OAAO;QACL,QAAQ,iBAAiB,OAAO;QAChC,eAAe;QACf,iBAAiB;IACnB;AACF;AACA,IAAI,qBAAqB,SAAU,EAAE;IACnC,IAAI,WAAW,GAAG,QAAQ;IAC1B,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAChC,OAAO;YACL,OAAO;QACT;IACF,GAAG;AACL;AAEA;;;;;;;;;AASA,GACA,IAAI,2BAA2B,SAAU,EAAE;IACzC,IAAI,WAAW,GAAG,QAAQ,EACxB,QAAQ,SAAS,IAAI;QAAC;KAAW;IACnC,IAAI,KAAK,wBAAwB,CAAC,EAAE,EAClC,aAAa,GAAG,UAAU,EAC1B,UAAU,GAAG,OAAO;IACtB,IAAI,KAAK,+BACP,SAAS,GAAG,MAAM,EAClB,gBAAgB,GAAG,aAAa,EAChC,kBAAkB,GAAG,eAAe;IACtC,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAChB,iBAAiB,EAAE,CAAC,EAAE,EACtB,oBAAoB,EAAE,CAAC,EAAE;IAC3B,IAAI,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAChC,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAChB,aAAa,EAAE,CAAC,EAAE,EAClB,gBAAgB,EAAE,CAAC,EAAE;IACvB,mFAAmF;IACnF,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAChB,WAAW,EAAE,CAAC,EAAE;IAClB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,IAAI,IAAI,IAAI;YACZ,IAAI,CAAC,YAAY;gBACf;YACF;YACA,IAAI;gBACF,mBAAmB,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,2BAA2B,OAAO,CAAC,aAAa,cAAc,CAAC,CAAC,EAAE,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,SAAS,CAAC,GAAG,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;YACvO,EAAE,OAAO,OAAO;gBACd;0DAAS;wBACP,MAAM,IAAI,MAAM,mFAAmF,MAAM,CAAC;oBAC5G;;gBACA;YACF;YACA,IAAI,CAAC,mBAAmB,OAAO,EAAE;gBAC/B;0DAAS;wBACP,IAAI;wBACJ,MAAM,IAAI,MAAM,+BAA+B,CAAC,KAAK;4BACnD,YAAY,QAAQ,UAAU;wBAChC,GAAG,EAAE,CAAC,aAAa,cAAc,CAAC,GAAG,OAAO,CAAC,aAAa,cAAc,CAAC,EAAE,EAAE;oBAC/E;;gBACA;YACF;YACA,cAAc,mBAAmB,OAAO,CAAC,UAAU;YACnD,kBAAkB,mBAAmB,OAAO;YAC5C;sDAAO;oBACL,kBAAkB;oBAClB,mBAAmB,OAAO,GAAG;gBAC/B;;QACF;6CAAG;QAAC;KAAW,GAAG,kDAAkD;IACpE,IAAI,CAAC,YAAY;QACf,sCAAsC;QACtC,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACpC;IACA,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,oBAAoB,MAAM,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,wBAAwB,QAAQ,EAAE;QACzG,OAAO;YACL,gBAAgB;YAChB,QAAQ;YACR,eAAe;YACf,iBAAiB;QACnB;IACF,GAAG;AACL;AACA,IAAI,kBAAkB,SAAU,EAAE;IAChC,IAAI,YAAY,GAAG,SAAS,EAC1B,YAAY,GAAG,SAAS,EACxB,UAAU,SAAS,IAAI;QAAC;QAAa;KAAY;IACnD,IAAI,KAAK,uBACP,iBAAiB,GAAG,cAAc,EAClC,gBAAgB,GAAG,aAAa,EAChC,kBAAkB,GAAG,eAAe;IACtC,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,2EAA2E;IAC3E,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAChB,WAAW,EAAE,CAAC,EAAE;IAClB,SAAS;QACP,gBAAgB;IAClB;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,gBAAgB;gBACnB;iDAAS;wBACP,MAAM,IAAI,MAAM;oBAClB;;gBACA,OAAO;YACT;YACA,IAAI,CAAC,aAAa,OAAO,EAAE;gBACzB,OAAO;YACT;YACA,IAAI,kBAAkB,cAAc,WAAW,SAAS;YACxD,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB,MAAM,CAAC,aAAa,OAAO,EAAE,KAAK;6CAAC,SAAU,GAAG;oBAChI,IAAI,CAAC,YAAY,eAAe;wBAC9B,iEAAiE;wBACjE;oBACF;oBACA,gCAAgC;oBAChC;qDAAS;4BACP,MAAM,IAAI,MAAM,2BAA2B,MAAM,CAAC,WAAW,mBAAmB,MAAM,CAAC;wBACzF;;gBACF;;YACA,OAAO;QACT;oCAAG,EAAE,GAAG,kDAAkD;IAC1D,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAChC,KAAK;QACL,WAAW;IACb;AACF;AACA,IAAI,kBAAkB,SAAU,OAAO;IACrC,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iBAAiB,SAAS;QACnD,WAAW;IACb,GAAG;AACL;AACA,IAAI,oBAAoB,SAAU,OAAO;IACvC,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iBAAiB,SAAS;QACnD,WAAW;IACb,GAAG;AACL;AACA,IAAI,oBAAoB,SAAU,OAAO;IACvC,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iBAAiB,SAAS;QACnD,WAAW;IACb,GAAG;AACL;AACA,IAAI,iBAAiB,SAAU,OAAO;IACpC,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iBAAiB,SAAS;QACnD,WAAW;IACb,GAAG;AACL;AACA,IAAI,gBAAgB,SAAU,EAAE;IAC9B,IAAI,WAAW,GAAG,QAAQ;IAC1B,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAChC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF,GAAG;AACL;AAEA;;;;;;;AAOA,GACA,IAAI,uBAAuB,SAAU,EAAE;IACrC,IAAI,YAAY,GAAG,SAAS;IAC5B,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAChC,WAAW;IACb,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iBAAiB;QACtC,WAAW;IACb,IAAI,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iBAAiB;QACvC,WAAW;IACb,IAAI,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,eAAe,MAAM,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,oBAAoB,MAAM,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iBAAiB;QAC9H,WAAW;IACb,KAAK,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,oBAAoB,MAAM,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iBAAiB;QACtF,WAAW;IACb;AACF;AACA,IAAI,YAAY;IACd,QAAQ;IACR,OAAO;IACP,UAAU;IACV,MAAM;IACN,QAAQ;IACR,UAAU;IACV,MAAM;IACN,OAAO;IACP,MAAM;IACN,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,KAAK;IACL,MAAM;IACN,MAAM;IACN,SAAS;IACT,MAAM;IACN,QAAQ;IACR,gBAAgB;IAChB,WAAW;IACX,aAAa;IACb,YAAY;IACZ,UAAU;IACV,OAAO;IACP,SAAS;IACT,QAAQ;AACV;AACA;IAAC,UAAU,KAAK;IAAE,UAAU,UAAU;IAAE,UAAU,OAAO;IAAE,UAAU,MAAM;IAAE,UAAU,GAAG;IAAE,UAAU,MAAM;IAAE,UAAU,GAAG;IAAE,UAAU,IAAI;IAAE,UAAU,IAAI;IAAE,UAAU,OAAO;IAAE,UAAU,IAAI;IAAE,UAAU,MAAM;IAAE,UAAU,cAAc;IAAE,UAAU,SAAS;IAAE,UAAU,WAAW;IAAE,UAAU,UAAU;IAAE,UAAU,QAAQ;IAAE,UAAU,KAAK;IAAE,UAAU,MAAM;IAAE,UAAU,OAAO;CAAC;AAEvX,gEAAgE;AAChE,+CAA+C;AAC/C,EAAE;AACF,2DAA2D;AAC3D,IAAI,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/solid/esm/CheckIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction CheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CheckIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,KAIlB,EAAE,MAAM;QAJU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJkB;IAKjB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}