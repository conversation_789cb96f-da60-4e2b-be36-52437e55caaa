{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/src/app/checkout/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { CheckIcon, ShieldCheckIcon } from '@heroicons/react/24/solid';\nimport PaymentModal from '@/components/PaymentModal';\n\nconst CheckoutPage = () => {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const [selectedPlan, setSelectedPlan] = useState('basic');\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n\n  const plans = {\n    basic: {\n      name: \"Basic Check\",\n      price: \"9.99\",\n      basePrice: 9.99,\n      unit: \"verification\",\n      description: \"Perfect for individual verification needs\",\n      features: [\n        \"Identity verification\",\n        \"Basic background check\",\n        \"Email support\",\n        \"24-hour processing\",\n        \"Secure document upload\",\n        \"PDF report delivery\"\n      ]\n    },\n    deep: {\n      name: \"Deep ID Scan\",\n      price: \"24.99\",\n      basePrice: 24.99,\n      unit: \"verification\",\n      description: \"Comprehensive verification for businesses\",\n      features: [\n        \"Everything in Basic Check\",\n        \"Advanced background screening\",\n        \"Criminal record check\",\n        \"Employment verification\",\n        \"Priority support\",\n        \"1-hour processing\",\n        \"API access\",\n        \"Bulk upload capability\"\n      ]\n    },\n    unlimited: {\n      name: \"Monthly Unlimited Pass\",\n      price: \"49.99\",\n      basePrice: 49.99,\n      unit: \"month\",\n      description: \"Unlimited verifications for growing businesses\",\n      features: [\n        \"Everything in Deep ID Scan\",\n        \"Unlimited verifications\",\n        \"Real-time processing\",\n        \"Dedicated account manager\",\n        \"Custom integrations\",\n        \"White-label reports\",\n        \"Advanced analytics\",\n        \"Phone support\",\n        \"SLA guarantee\"\n      ]\n    }\n  };\n\n  useEffect(() => {\n    const plan = searchParams.get('plan');\n    if (plan && plans[plan as keyof typeof plans]) {\n      setSelectedPlan(plan);\n    }\n  }, [searchParams]);\n\n  const currentPlan = plans[selectedPlan as keyof typeof plans];\n\n  const handlePaymentSuccess = (details: any) => {\n    console.log('Payment successful:', details);\n    setIsProcessing(true);\n\n    // Simulate API call to process the payment\n    setTimeout(() => {\n      setIsProcessing(false);\n      // Redirect to dashboard with success message\n      router.push('/dashboard?payment=success');\n    }, 2000);\n  };\n\n  const openPaymentModal = () => {\n    setIsModalOpen(true);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-12\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <div className=\"flex justify-center mb-4\">\n            <ShieldCheckIcon className=\"h-12 w-12 text-blue-600\" />\n          </div>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Complete Your Purchase</h1>\n          <p className=\"text-lg text-gray-600\">\n            Secure checkout powered by PayPal with guest payment options\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Plan Selection */}\n          <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Select Your Plan</h2>\n            \n            <div className=\"space-y-4\">\n              {Object.entries(plans).map(([key, plan]) => (\n                <div\n                  key={key}\n                  className={`border-2 rounded-xl p-4 cursor-pointer transition-colors duration-200 ${\n                    selectedPlan === key\n                      ? 'border-blue-600 bg-blue-50'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }`}\n                  onClick={() => setSelectedPlan(key)}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900\">{plan.name}</h3>\n                      <p className=\"text-gray-600\">{plan.description}</p>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-2xl font-bold text-gray-900\">\n                        ${plan.price}\n                        {key === 'unlimited' && (\n                          <span className=\"text-sm text-gray-600 font-normal\">/month</span>\n                        )}\n                      </div>\n                      <div className={`w-4 h-4 rounded-full border-2 mt-2 ${\n                        selectedPlan === key\n                          ? 'bg-blue-600 border-blue-600'\n                          : 'border-gray-300'\n                      }`}>\n                        {selectedPlan === key && (\n                          <CheckIcon className=\"h-3 w-3 text-white m-0.5\" />\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Selected Plan Features */}\n            <div className=\"mt-8 p-6 bg-gray-50 rounded-xl\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                {currentPlan.name} includes:\n              </h3>\n              <ul className=\"space-y-2\">\n                {currentPlan.features.map((feature, index) => (\n                  <li key={index} className=\"flex items-center space-x-2\">\n                    <CheckIcon className=\"h-4 w-4 text-green-500 flex-shrink-0\" />\n                    <span className=\"text-gray-700\">{feature}</span>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n\n          {/* Payment Section */}\n          <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Payment Details</h2>\n            \n            {/* Order Summary */}\n            <div className=\"bg-gray-50 rounded-xl p-6 mb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Order Summary</h3>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-gray-700\">{currentPlan.name}</span>\n                <span className=\"font-semibold\">${currentPlan.price}</span>\n              </div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-gray-700\">Processing Fee</span>\n                <span className=\"font-semibold\">$0.00</span>\n              </div>\n              <hr className=\"my-4\" />\n              <div className=\"flex justify-between items-center text-lg font-bold\">\n                <span>Total</span>\n                <span>${currentPlan.price} USD</span>\n              </div>\n            </div>\n\n            {/* Payment Section */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Payment Method</h3>\n\n              {isProcessing ? (\n                <div className=\"flex items-center justify-center py-8\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"></div>\n                  <span className=\"text-gray-700\">Processing payment...</span>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  <button\n                    onClick={openPaymentModal}\n                    className=\"w-full bg-blue-600 text-white py-4 px-6 rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200 text-lg\"\n                  >\n                    Pay with Card (No PayPal Account Required)\n                  </button>\n                  <p className=\"text-sm text-gray-600 text-center\">\n                    Customize your purchase quantity and pay securely with debit/credit card\n                  </p>\n                  <div className=\"flex items-center justify-center space-x-2 text-xs text-gray-500\">\n                    <span>🔒 SSL Secured</span>\n                    <span>•</span>\n                    <span>💳 Guest Checkout</span>\n                    <span>•</span>\n                    <span>⚡ Instant Access</span>\n                  </div>\n                </div>\n              )}\n\n              {/* Security Badges */}\n              <div className=\"mt-6 pt-6 border-t border-gray-200\">\n                <div className=\"flex items-center justify-center space-x-6 text-sm text-gray-600\">\n                  <div className=\"flex items-center space-x-1\">\n                    <ShieldCheckIcon className=\"h-4 w-4 text-green-500\" />\n                    <span>SSL Secured</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    <ShieldCheckIcon className=\"h-4 w-4 text-blue-500\" />\n                    <span>PayPal Protected</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    <ShieldCheckIcon className=\"h-4 w-4 text-purple-500\" />\n                    <span>GDPR Compliant</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Money Back Guarantee */}\n        <div className=\"mt-12 text-center bg-white rounded-2xl shadow-lg p-8\">\n          <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n            30-Day Money Back Guarantee\n          </h3>\n          <p className=\"text-gray-600 max-w-2xl mx-auto\">\n            We're confident you'll love our verification services. If you're not completely satisfied \n            with your results, we'll provide a full refund within 30 days of purchase.\n          </p>\n        </div>\n      </div>\n\n      {/* Payment Modal - Temporarily disabled */}\n      {/* <PaymentModal\n        isOpen={isModalOpen}\n        onClose={() => setIsModalOpen(false)}\n        selectedPlan={{\n          name: currentPlan.name,\n          basePrice: currentPlan.basePrice,\n          description: currentPlan.description,\n          unit: currentPlan.unit\n        }}\n        onPaymentSuccess={handlePaymentSuccess}\n      /> */}\n    </div>\n  );\n};\n\nexport default CheckoutPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAOA,MAAM,eAAe;;IACnB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,QAAQ;QACZ,OAAO;YACL,MAAM;YACN,OAAO;YACP,WAAW;YACX,MAAM;YACN,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA,MAAM;YACJ,MAAM;YACN,OAAO;YACP,WAAW;YACX,MAAM;YACN,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA,WAAW;YACT,MAAM;YACN,OAAO;YACP,WAAW;YACX,MAAM;YACN,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,OAAO,aAAa,GAAG,CAAC;YAC9B,IAAI,QAAQ,KAAK,CAAC,KAA2B,EAAE;gBAC7C,gBAAgB;YAClB;QACF;iCAAG;QAAC;KAAa;IAEjB,MAAM,cAAc,KAAK,CAAC,aAAmC;IAE7D,MAAM,uBAAuB,CAAC;QAC5B,QAAQ,GAAG,CAAC,uBAAuB;QACnC,gBAAgB;QAEhB,2CAA2C;QAC3C,WAAW;YACT,gBAAgB;YAChB,6CAA6C;YAC7C,OAAO,IAAI,CAAC;QACd,GAAG;IACL;IAEA,MAAM,mBAAmB;QACvB,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,8NAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;;;;;;sCAE7B,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAEtD,6LAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,OAAO,GAAG,CAAC;4CAAC,CAAC,KAAK,KAAK;6DACrC,6LAAC;4CAEC,WAAW,AAAC,yEAIX,OAHC,iBAAiB,MACb,+BACA;4CAEN,SAAS,IAAM,gBAAgB;sDAE/B,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAuC,KAAK,IAAI;;;;;;0EAC9D,6LAAC;gEAAE,WAAU;0EAAiB,KAAK,WAAW;;;;;;;;;;;;kEAEhD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEAAmC;oEAC9C,KAAK,KAAK;oEACX,QAAQ,6BACP,6LAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;0EAGxD,6LAAC;gEAAI,WAAW,AAAC,sCAIhB,OAHC,iBAAiB,MACb,gCACA;0EAEH,iBAAiB,qBAChB,6LAAC,kNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CA1BxB;;;;;;;;;;;8CAoCX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDACX,YAAY,IAAI;gDAAC;;;;;;;sDAEpB,6LAAC;4CAAG,WAAU;sDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAClC,6LAAC;oDAAe,WAAU;;sEACxB,6LAAC,kNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;mDAF1B;;;;;;;;;;;;;;;;;;;;;;sCAUjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAiB,YAAY,IAAI;;;;;;8DACjD,6LAAC;oDAAK,WAAU;;wDAAgB;wDAAE,YAAY,KAAK;;;;;;;;;;;;;sDAErD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAG,WAAU;;;;;;sDACd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;;wDAAK;wDAAE,YAAY,KAAK;wDAAC;;;;;;;;;;;;;;;;;;;8CAK9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;wCAEnD,6BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;qGAGlC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DAGjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;sDAMZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8NAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;;0EAC3B,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8NAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;;0EAC3B,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8NAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;;0EAC3B,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuC;;;;;;sCAGrD,6LAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;;;;;;;;;;;;AAqBzD;GAjQM;;QACW,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAFhC;uCAmQS", "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/solid/esm/CheckIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction CheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CheckIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,KAIlB,EAAE,MAAM;QAJU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJkB;IAKjB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}