{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { ShieldCheckIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/solid';\n\nconst LoginPage = () => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState<{[key: string]: string}>({});\n  const router = useRouter();\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors: {[key: string]: string} = {};\n\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setIsLoading(true);\n    \n    // Simulate API call\n    setTimeout(() => {\n      setIsLoading(false);\n      // For demo purposes, redirect to dashboard\n      router.push('/dashboard');\n    }, 1500);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"flex justify-center\">\n          <div className=\"flex items-center space-x-2\">\n            <ShieldCheckIcon className=\"h-10 w-10 text-blue-600\" />\n            <span className=\"text-2xl font-bold text-gray-900\">iVerifyPro</span>\n          </div>\n        </div>\n        <h2 className=\"mt-6 text-center text-3xl font-bold text-gray-900\">\n          Sign in to your account\n        </h2>\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          Or{' '}\n          <Link href=\"/signup\" className=\"font-medium text-blue-600 hover:text-blue-500\">\n            create a new account\n          </Link>\n        </p>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow-lg sm:rounded-2xl sm:px-10\">\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  className={`appearance-none block w-full px-3 py-3 border rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                    errors.email ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                  placeholder=\"Enter your email\"\n                />\n                {errors.email && (\n                  <p className=\"mt-2 text-sm text-red-600\">{errors.email}</p>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"current-password\"\n                  value={formData.password}\n                  onChange={handleChange}\n                  className={`appearance-none block w-full px-3 py-3 pr-10 border rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                    errors.password ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                  placeholder=\"Enter your password\"\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                  ) : (\n                    <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                  )}\n                </button>\n                {errors.password && (\n                  <p className=\"mt-2 text-sm text-red-600\">{errors.password}</p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <input\n                  id=\"remember-me\"\n                  name=\"remember-me\"\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-900\">\n                  Remember me\n                </label>\n              </div>\n\n              <div className=\"text-sm\">\n                <Link href=\"/forgot-password\" className=\"font-medium text-blue-600 hover:text-blue-500\">\n                  Forgot your password?\n                </Link>\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\"\n              >\n                {isLoading ? (\n                  <div className=\"flex items-center\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Signing in...\n                  </div>\n                ) : (\n                  'Sign in'\n                )}\n              </button>\n            </div>\n          </form>\n\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">Or continue with</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6 grid grid-cols-2 gap-3\">\n              <button className=\"w-full inline-flex justify-center py-3 px-4 border border-gray-300 rounded-xl shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200\">\n                <span>Google</span>\n              </button>\n              <button className=\"w-full inline-flex justify-center py-3 px-4 border border-gray-300 rounded-xl shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200\">\n                <span>Microsoft</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;AALA;;;;;AAOA,MAAM,YAAY;;IAChB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAC/D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QACD,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAqC,CAAC;QAE5C,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC/C,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YACvC,UAAU,QAAQ,GAAG;QACvB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,aAAa;QAEb,oBAAoB;QACpB,WAAW;YACT,aAAa;YACb,2CAA2C;YAC3C,OAAO,IAAI,CAAC;QACd,GAAG;IACL;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8NAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;8CAC3B,6LAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAGvD,6LAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAGlE,6LAAC;wBAAE,WAAU;;4BAAyC;4BACjD;0CACH,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,WAAU;0CAAgD;;;;;;;;;;;;;;;;;;0BAMnF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;4BAAY,UAAU;;8CACpC,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAW,AAAC,mKAEX,OADC,OAAO,KAAK,GAAG,mBAAmB;oDAEpC,aAAY;;;;;;gDAEb,OAAO,KAAK,kBACX,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,KAAK;;;;;;;;;;;;;;;;;;8CAK5D,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,eAAe,SAAS;oDAC9B,cAAa;oDACb,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,WAAW,AAAC,yKAEX,OADC,OAAO,QAAQ,GAAG,mBAAmB;oDAEvC,aAAY;;;;;;8DAEd,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,gBAAgB,CAAC;8DAE/B,6BACC,6LAAC,wNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;iHAExB,6LAAC,8MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;gDAGtB,OAAO,QAAQ,kBACd,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,QAAQ;;;;;;;;;;;;;;;;;;8CAK/D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,WAAU;;;;;;8DAEZ,6LAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAAmC;;;;;;;;;;;;sDAK5E,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;0DAAgD;;;;;;;;;;;;;;;;;8CAM5F,6LAAC;8CACC,cAAA,6LAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,0BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;gDAAuE;;;;;;uFAIxF;;;;;;;;;;;;;;;;;sCAMR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA8B;;;;;;;;;;;;;;;;;8CAIlD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC;0DAAK;;;;;;;;;;;sDAER,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GA1MM;;QAQW,qIAAA,CAAA,YAAS;;;KARpB;uCA4MS", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/solid/esm/EyeIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M1.323 11.447C2.811 6.976 7.028 3.75 12.001 3.75c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113-1.487 4.471-5.705 7.697-10.677 7.697-4.97 0-9.186-3.223-10.675-7.69a1.762 1.762 0 0 1 0-1.113ZM17.25 12a5.25 5.25 0 1 1-10.5 0 5.25 5.25 0 0 1 10.5 0Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,QAAQ,KAIhB,EAAE,MAAM;QAJQ,EACf,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJgB;IAKf,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,GAAG;IACL,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC3C,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/solid/esm/EyeSlashIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EyeSlashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M3.53 2.47a.75.75 0 0 0-1.06 1.06l18 18a.75.75 0 1 0 1.06-1.06l-18-18ZM22.676 12.553a11.249 11.249 0 0 1-2.631 4.31l-3.099-3.099a5.25 5.25 0 0 0-6.71-6.71L7.759 4.577a11.217 11.217 0 0 1 4.242-.827c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M15.75 12c0 .18-.013.357-.037.53l-4.244-4.243A3.75 3.75 0 0 1 15.75 12ZM12.53 15.713l-4.243-4.244a3.75 3.75 0 0 0 4.244 4.243Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M6.75 12c0-.619.107-1.213.304-1.764l-3.1-3.1a11.25 11.25 0 0 0-2.63 4.31c-.12.362-.12.752 0 1.114 1.489 4.467 5.704 7.69 10.675 7.69 1.5 0 2.933-.294 4.242-.827l-2.477-2.477A5.25 5.25 0 0 1 6.75 12Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeSlashIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,KAIrB,EAAE,MAAM;QAJa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJqB;IAKpB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,GAAG;IACL,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC3C,GAAG;IACL,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC3C,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}