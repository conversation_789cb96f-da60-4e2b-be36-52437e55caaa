{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/src/app/not-found.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { ShieldCheckIcon, HomeIcon } from '@heroicons/react/24/solid';\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md text-center\">\n        <div className=\"flex justify-center mb-6\">\n          <ShieldCheckIcon className=\"h-16 w-16 text-blue-600\" />\n        </div>\n        \n        <h1 className=\"text-6xl font-bold text-gray-900 mb-4\">404</h1>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Page Not Found</h2>\n        \n        <p className=\"text-gray-600 mb-8 max-w-md mx-auto\">\n          Sorry, we couldn't find the page you're looking for. \n          The page might have been moved, deleted, or you entered the wrong URL.\n        </p>\n        \n        <div className=\"space-y-4\">\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200\"\n          >\n            <HomeIcon className=\"h-5 w-5 mr-2\" />\n            Go Home\n          </Link>\n          \n          <div className=\"text-sm text-gray-500\">\n            <Link href=\"/pricing\" className=\"text-blue-600 hover:text-blue-500 mr-4\">\n              View Pricing\n            </Link>\n            <Link href=\"/dashboard\" className=\"text-blue-600 hover:text-blue-500 mr-4\">\n              Dashboard\n            </Link>\n            <Link href=\"/login\" className=\"text-blue-600 hover:text-blue-500\">\n              Login\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,2NAAA,CAAA,kBAAe;wBAAC,WAAU;;;;;;;;;;;8BAG7B,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAEtD,8OAAC;oBAAE,WAAU;8BAAsC;;;;;;8BAKnD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAyC;;;;;;8CAGzE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAyC;;;;;;8CAG3E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9E", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/iVerifyPro/node_modules/%40heroicons/react/24/solid/esm/HomeIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction HomeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.47 3.841a.75.75 0 0 1 1.06 0l8.69 8.69a.75.75 0 1 0 1.06-1.061l-8.689-8.69a2.25 2.25 0 0 0-3.182 0l-8.69 8.69a.75.75 0 1 0 1.061 1.06l8.69-8.689Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"m12 5.432 8.159 8.159c.03.03.06.058.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 0 1-.75-.75v-4.5a.75.75 0 0 0-.75-.75h-3a.75.75 0 0 0-.75.75V21a.75.75 0 0 1-.75.75H5.625a1.875 1.875 0 0 1-1.875-1.875v-6.198a2.29 2.29 0 0 0 .091-.086L12 5.432Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(HomeIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,GAAG;IACL,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC3C,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}