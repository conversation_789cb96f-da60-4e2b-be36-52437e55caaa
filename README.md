# iVerifyPro - Professional Identity Verification SaaS

A modern, responsive SaaS website for identity verification and background check services built with Next.js, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Modern Design**: Clean, professional interface with blue/white theme
- **Responsive Layout**: Mobile-first design that works on all devices
- **PayPal Integration**: Secure payment processing with guest checkout support
- **Three Pricing Plans**:
  - Basic Check ($9.99)
  - Deep ID Scan ($24.99)
  - Monthly Unlimited Pass ($49.99)
- **User Dashboard**: Complete verification management interface
- **Authentication**: Login/signup pages with form validation
- **Trust Indicators**: Security badges and compliance messaging

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Heroicons
- **Payments**: PayPal React SDK
- **Fonts**: Inter (Google Fonts)

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd iVerifyPro
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🔧 Configuration

### PayPal Setup
To enable PayPal payments, update the `clientId` in `src/app/checkout/page.tsx`:

```typescript
const paypalOptions = {
  clientId: "your-paypal-client-id", // Replace with your actual PayPal client ID
  currency: "USD",
  intent: "capture",
  enableFunding: "card" as any,
};
```

## 📱 Pages

- **Homepage** (`/`) - Hero section with trust badges and features
- **Pricing** (`/pricing`) - Three-tier pricing with feature comparison
- **Login** (`/login`) - User authentication
- **Signup** (`/signup`) - User registration with validation
- **Dashboard** (`/dashboard`) - Verification management interface
- **Checkout** (`/checkout`) - PayPal-powered payment processing

## 🎨 Design Features

- **Rounded Elements**: Consistent use of `rounded-2xl` for modern look
- **Shadows**: Card shadows with hover effects
- **Blue Theme**: Primary blue (#3b82f6) with complementary colors
- **Typography**: Inter font for clean, professional appearance
- **Animations**: Smooth transitions and hover effects

## 🔒 Security Features

- SSL encryption messaging
- GDPR compliance indicators
- Secure payment processing
- Data protection badges

## 🚀 Deployment

The application is ready for deployment on platforms like Vercel, Netlify, or any Node.js hosting service.

### Vercel Deployment
```bash
npm run build
```

Then deploy to Vercel or run:
```bash
npm start
```

## 📄 License

This project is for demonstration purposes. Please ensure you have proper licensing for any production use.

## 🤝 Contributing

This is a demo project. For production use, consider adding:
- Real authentication system
- Database integration
- Actual verification APIs
- Enhanced security measures
- Testing suite
