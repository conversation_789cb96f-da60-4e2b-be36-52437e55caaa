import Link from 'next/link';
import {
  ShieldCheckIcon,
  ClockIcon,
  LockClosedIcon,
  CheckCircleIcon,
  StarIcon
} from '@heroicons/react/24/solid';

export default function Home() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 to-indigo-100 py-20 lg:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Verify IDs & Backgrounds
              <span className="block text-blue-600">in Minutes</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Professional identity verification and background check services.
              Fast, secure, and reliable verification solutions for businesses and individuals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/pricing"
                className="bg-blue-600 text-white px-8 py-4 rounded-2xl text-lg font-semibold hover:bg-blue-700 transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                Start Verification
              </Link>
              <Link
                href="/dashboard"
                className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-2xl text-lg font-semibold hover:bg-blue-50 transition-colors duration-200"
              >
                View Dashboard
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Badges */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <LockClosedIcon className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Encrypted System</h3>
              <p className="text-gray-600">
                Bank-level encryption ensures your data is always protected and secure.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <ClockIcon className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Fast Response</h3>
              <p className="text-gray-600">
                Get verification results in minutes, not days. Quick turnaround guaranteed.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <ShieldCheckIcon className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Secure Checkout</h3>
              <p className="text-gray-600">
                Safe and secure payment processing with multiple payment options.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose iVerifyPro?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Professional verification services trusted by thousands of businesses worldwide.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: CheckCircleIcon,
                title: "Instant Verification",
                description: "Real-time identity verification with immediate results"
              },
              {
                icon: ShieldCheckIcon,
                title: "Comprehensive Checks",
                description: "Complete background screening and identity validation"
              },
              {
                icon: StarIcon,
                title: "99.9% Accuracy",
                description: "Industry-leading accuracy rates for all verification types"
              },
              {
                icon: LockClosedIcon,
                title: "GDPR Compliant",
                description: "Fully compliant with international privacy regulations"
              },
              {
                icon: ClockIcon,
                title: "24/7 Support",
                description: "Round-the-clock customer support for all your needs"
              },
              {
                icon: CheckCircleIcon,
                title: "API Integration",
                description: "Easy integration with your existing systems and workflows"
              }
            ].map((feature, index) => (
              <div key={index} className="bg-white p-6 rounded-2xl shadow-md hover:shadow-lg transition-shadow duration-200">
                <feature.icon className="h-12 w-12 text-blue-600 mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied customers who trust iVerifyPro for their verification needs.
          </p>
          <Link
            href="/pricing"
            className="bg-white text-blue-600 px-8 py-4 rounded-2xl text-lg font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1 inline-block"
          >
            Choose Your Plan
          </Link>
        </div>
      </section>
    </div>
  );
}
