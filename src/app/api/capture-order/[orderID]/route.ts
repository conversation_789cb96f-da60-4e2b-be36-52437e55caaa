import { NextRequest, NextResponse } from 'next/server';

const PAYPAL_API = 'https://api-m.paypal.com';
const PAYPAL_CLIENT_ID = 'AVpJKjl6nABg0Y0Jekdmi58TNAJLbtYsyJ3e9Qruj0hAbK3c4BhZLa6mJWv2lpNLUC_PeTFLsy-vHt-N';
const PAYPAL_SECRET = process.env.PAYPAL_SECRET || 'YOUR_REAL_SECRET'; // Add your real secret to .env.local

async function getAccessToken() {
  const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_SECRET}`).toString('base64');
  
  const response = await fetch(`${PAYPAL_API}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: 'grant_type=client_credentials'
  });

  const data = await response.json();
  return data.access_token;
}

export async function POST(
  request: NextRequest,
  { params }: { params: { orderID: string } }
) {
  try {
    const { orderID } = params;
    
    const token = await getAccessToken();
    
    const response = await fetch(`${PAYPAL_API}/v2/checkout/orders/${orderID}/capture`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    const captureData = await response.json();
    
    if (!response.ok) {
      console.error('PayPal Capture Error:', captureData);
      return NextResponse.json(
        { error: 'Failed to capture PayPal payment', details: captureData },
        { status: 500 }
      );
    }

    // Log successful payment for your records
    console.log('Payment captured successfully:', {
      orderID,
      payerEmail: captureData.payer?.email_address,
      amount: captureData.purchase_units?.[0]?.payments?.captures?.[0]?.amount,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({ 
      status: 'success', 
      details: captureData,
      orderID: orderID
    });
  } catch (error) {
    console.error('Capture order error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
