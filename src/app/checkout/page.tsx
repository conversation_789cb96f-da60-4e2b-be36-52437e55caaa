'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { CheckIcon, ShieldCheckIcon } from '@heroicons/react/24/solid';
import PaymentModal from '@/components/PaymentModal';

const CheckoutPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedPlan, setSelectedPlan] = useState('basic');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const plans = {
    basic: {
      name: "Basic Check",
      price: "9.99",
      basePrice: 9.99,
      unit: "verification",
      description: "Perfect for individual verification needs",
      features: [
        "Identity verification",
        "Basic background check",
        "Email support",
        "24-hour processing",
        "Secure document upload",
        "PDF report delivery"
      ]
    },
    deep: {
      name: "Deep ID Scan",
      price: "24.99",
      basePrice: 24.99,
      unit: "verification",
      description: "Comprehensive verification for businesses",
      features: [
        "Everything in Basic Check",
        "Advanced background screening",
        "Criminal record check",
        "Employment verification",
        "Priority support",
        "1-hour processing",
        "API access",
        "Bulk upload capability"
      ]
    },
    unlimited: {
      name: "Monthly Unlimited Pass",
      price: "49.99",
      basePrice: 49.99,
      unit: "month",
      description: "Unlimited verifications for growing businesses",
      features: [
        "Everything in Deep ID Scan",
        "Unlimited verifications",
        "Real-time processing",
        "Dedicated account manager",
        "Custom integrations",
        "White-label reports",
        "Advanced analytics",
        "Phone support",
        "SLA guarantee"
      ]
    }
  };

  useEffect(() => {
    const plan = searchParams.get('plan');
    if (plan && plans[plan as keyof typeof plans]) {
      setSelectedPlan(plan);
    }
  }, [searchParams]);

  const currentPlan = plans[selectedPlan as keyof typeof plans];

  const handlePaymentSuccess = (details: any) => {
    console.log('Payment successful:', details);
    setIsProcessing(true);

    // Simulate API call to process the payment
    setTimeout(() => {
      setIsProcessing(false);
      // Redirect to dashboard with success message
      router.push('/dashboard?payment=success');
    }, 2000);
  };

  const openPaymentModal = () => {
    setIsModalOpen(true);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <ShieldCheckIcon className="h-12 w-12 text-blue-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Complete Your Purchase</h1>
          <p className="text-lg text-gray-600">
            Secure checkout powered by PayPal with guest payment options
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Plan Selection */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Select Your Plan</h2>
            
            <div className="space-y-4">
              {Object.entries(plans).map(([key, plan]) => (
                <div
                  key={key}
                  className={`border-2 rounded-xl p-4 cursor-pointer transition-colors duration-200 ${
                    selectedPlan === key
                      ? 'border-blue-600 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedPlan(key)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{plan.name}</h3>
                      <p className="text-gray-600">{plan.description}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">
                        ${plan.price}
                        {key === 'unlimited' && (
                          <span className="text-sm text-gray-600 font-normal">/month</span>
                        )}
                      </div>
                      <div className={`w-4 h-4 rounded-full border-2 mt-2 ${
                        selectedPlan === key
                          ? 'bg-blue-600 border-blue-600'
                          : 'border-gray-300'
                      }`}>
                        {selectedPlan === key && (
                          <CheckIcon className="h-3 w-3 text-white m-0.5" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Selected Plan Features */}
            <div className="mt-8 p-6 bg-gray-50 rounded-xl">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {currentPlan.name} includes:
              </h3>
              <ul className="space-y-2">
                {currentPlan.features.map((feature, index) => (
                  <li key={index} className="flex items-center space-x-2">
                    <CheckIcon className="h-4 w-4 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Payment Section */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Payment Details</h2>
            
            {/* Order Summary */}
            <div className="bg-gray-50 rounded-xl p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-700">{currentPlan.name}</span>
                <span className="font-semibold">${currentPlan.price}</span>
              </div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-700">Processing Fee</span>
                <span className="font-semibold">$0.00</span>
              </div>
              <hr className="my-4" />
              <div className="flex justify-between items-center text-lg font-bold">
                <span>Total</span>
                <span>${currentPlan.price} USD</span>
              </div>
            </div>

            {/* Payment Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Payment Method</h3>

              {isProcessing ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                  <span className="text-gray-700">Processing payment...</span>
                </div>
              ) : (
                <div className="space-y-3">
                  <button
                    onClick={openPaymentModal}
                    className="w-full bg-blue-600 text-white py-4 px-6 rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200 text-lg"
                  >
                    Pay with Card (No PayPal Account Required)
                  </button>
                  <p className="text-sm text-gray-600 text-center">
                    Customize your purchase quantity and pay securely with debit/credit card
                  </p>
                  <div className="flex items-center justify-center space-x-2 text-xs text-gray-500">
                    <span>🔒 SSL Secured</span>
                    <span>•</span>
                    <span>💳 Guest Checkout</span>
                    <span>•</span>
                    <span>⚡ Instant Access</span>
                  </div>
                </div>
              )}

              {/* Security Badges */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex items-center justify-center space-x-6 text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <ShieldCheckIcon className="h-4 w-4 text-green-500" />
                    <span>SSL Secured</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <ShieldCheckIcon className="h-4 w-4 text-blue-500" />
                    <span>PayPal Protected</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <ShieldCheckIcon className="h-4 w-4 text-purple-500" />
                    <span>GDPR Compliant</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Money Back Guarantee */}
        <div className="mt-12 text-center bg-white rounded-2xl shadow-lg p-8">
          <h3 className="text-xl font-bold text-gray-900 mb-4">
            30-Day Money Back Guarantee
          </h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            We're confident you'll love our verification services. If you're not completely satisfied 
            with your results, we'll provide a full refund within 30 days of purchase.
          </p>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        selectedPlan={{
          name: currentPlan.name,
          basePrice: currentPlan.basePrice,
          description: currentPlan.description,
          unit: currentPlan.unit
        }}
        onPaymentSuccess={handlePaymentSuccess}
      />
    </div>
  );
};

export default CheckoutPage;
