import Link from 'next/link';
import { CheckIcon, StarIcon } from '@heroicons/react/24/solid';

const PricingPage = () => {
  const plans = [
    {
      name: "Basic Check",
      price: "$9.99",
      description: "Perfect for individual verification needs",
      features: [
        "Identity verification",
        "Basic background check",
        "Email support",
        "24-hour processing",
        "Secure document upload",
        "PDF report delivery"
      ],
      popular: false,
      buttonText: "Get Basic Check",
      buttonStyle: "border-2 border-blue-600 text-blue-600 hover:bg-blue-50"
    },
    {
      name: "Deep ID Scan",
      price: "$24.99",
      description: "Comprehensive verification for businesses",
      features: [
        "Everything in Basic Check",
        "Advanced background screening",
        "Criminal record check",
        "Employment verification",
        "Priority support",
        "1-hour processing",
        "API access",
        "Bulk upload capability"
      ],
      popular: true,
      buttonText: "Get Deep Scan",
      buttonStyle: "bg-blue-600 text-white hover:bg-blue-700"
    },
    {
      name: "Monthly Unlimited Pass",
      price: "$49.99",
      description: "Unlimited verifications for growing businesses",
      features: [
        "Everything in Deep ID Scan",
        "Unlimited verifications",
        "Real-time processing",
        "Dedicated account manager",
        "Custom integrations",
        "White-label reports",
        "Advanced analytics",
        "Phone support",
        "SLA guarantee"
      ],
      popular: false,
      buttonText: "Get Unlimited",
      buttonStyle: "border-2 border-purple-600 text-purple-600 hover:bg-purple-50"
    }
  ];

  return (
    <div className="bg-white py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Choose Your Verification Plan
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Select the perfect plan for your verification needs. All plans include secure processing, 
            encrypted data handling, and our satisfaction guarantee.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <div
              key={index}
              className={`relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-200 p-8 ${
                plan.popular ? 'ring-2 ring-blue-600 scale-105' : ''
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center space-x-1">
                    <StarIcon className="h-4 w-4" />
                    <span>Most Popular</span>
                  </div>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                <div className="text-4xl font-bold text-gray-900 mb-2">
                  {plan.price}
                  {plan.name === "Monthly Unlimited Pass" && (
                    <span className="text-lg text-gray-600 font-normal">/month</span>
                  )}
                </div>
                <p className="text-gray-600">{plan.description}</p>
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start space-x-3">
                    <CheckIcon className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              <Link
                href="/checkout"
                className={`w-full block text-center px-6 py-3 rounded-xl font-semibold transition-colors duration-200 ${plan.buttonStyle}`}
              >
                {plan.buttonText}
              </Link>
            </div>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="bg-gray-50 rounded-2xl p-8 mb-16">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Trusted by 10,000+ Customers Worldwide
            </h2>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">99.9%</div>
              <div className="text-gray-600">Accuracy Rate</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-600 mb-2">&lt; 1hr</div>
              <div className="text-gray-600">Average Processing</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-600 mb-2">24/7</div>
              <div className="text-gray-600">Customer Support</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-orange-600 mb-2">SSL</div>
              <div className="text-gray-600">Encrypted Security</div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">
            Frequently Asked Questions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
            <div className="bg-white p-6 rounded-xl shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                How long does verification take?
              </h3>
              <p className="text-gray-600">
                Basic checks are completed within 24 hours, while Deep ID Scans are processed within 1 hour. 
                Unlimited Pass subscribers get real-time processing.
              </p>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Is my data secure?
              </h3>
              <p className="text-gray-600">
                Yes, we use bank-level encryption and are fully GDPR compliant. 
                Your data is protected with the highest security standards.
              </p>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Can I cancel anytime?
              </h3>
              <p className="text-gray-600">
                Yes, you can cancel your Monthly Unlimited Pass at any time. 
                Individual checks are one-time purchases with no recurring charges.
              </p>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Do you offer refunds?
              </h3>
              <p className="text-gray-600">
                We offer a 100% satisfaction guarantee. If you're not satisfied with your verification results, 
                we'll provide a full refund within 30 days.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingPage;
