'use client';

import { useState, useEffect } from 'react';
import { XMarkIcon, MinusIcon, PlusIcon } from '@heroicons/react/24/outline';
import Script from 'next/script';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedPlan: {
    name: string;
    basePrice: number;
    description: string;
    unit: string;
  };
  onPaymentSuccess: (details: any) => void;
}

const PaymentModal = ({ isOpen, onClose, selectedPlan, onPaymentSuccess }: PaymentModalProps) => {
  const [quantity, setQuantity] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);
  const [paypalLoaded, setPaypalLoaded] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setQuantity(1);
    }
  }, [isOpen]);

  useEffect(() => {
    if (paypalLoaded && isOpen && (window as any).paypal) {
      initializePayPalButtons();
    }
  }, [paypalLoaded, isOpen, quantity]);

  if (!isOpen) return null;

  const minQuantity = 1;
  const maxQuantity = selectedPlan.unit === 'month' ? 12 : 50;

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= minQuantity && newQuantity <= maxQuantity) {
      setQuantity(newQuantity);
    }
  };

  const totalPrice = (selectedPlan.basePrice * quantity).toFixed(2);
  const pricePerUnit = selectedPlan.basePrice.toFixed(2);

  const initializePayPalButtons = () => {
    const container = document.getElementById('paypal-button-container');
    if (container && (window as any).paypal) {
      container.innerHTML = '';

      (window as any).paypal.Buttons({
        fundingSource: (window as any).paypal.FUNDING.CARD,
        style: {
          layout: 'vertical',
          shape: 'pill',
          color: 'blue',
          label: 'pay'
        },
        createOrder: async function(data: any, actions: any) {
          const response = await fetch('/api/create-order', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              plan: `${quantity}x ${selectedPlan.name}`,
              amount: totalPrice,
              quantity: quantity
            })
          });
          const orderData = await response.json();
          return orderData.id;
        },
        onApprove: async function(data: any, actions: any) {
          setIsProcessing(true);
          try {
            const response = await fetch(`/api/capture-order/${data.orderID}`, {
              method: 'POST'
            });
            const details = await response.json();

            if (details.status === 'success') {
              alert('✅ Payment Successful');
              onPaymentSuccess(details);
              onClose();
              window.location.href = '/dashboard?payment=success';
            }
          } catch (error) {
            console.error('Payment error:', error);
            alert('Payment failed. Please try again.');
          } finally {
            setIsProcessing(false);
          }
        }
      }).render('#paypal-button-container');
    }
  };

  const getQuantityLabel = () => {
    if (selectedPlan.unit === 'month') {
      return quantity === 1 ? '1 Month' : `${quantity} Months`;
    }
    return quantity === 1 ? '1 Verification' : `${quantity} Verifications`;
  };

  return (
    <>
      {/* PayPal Script */}
      <Script
        src="https://www.paypal.com/sdk/js?client-id=AVpJKjl6nABg0Y0Jekdmi58TNAJLbtYsyJ3e9Qruj0hAbK3c4BhZLa6mJWv2lpNLUC_PeTFLsy-vHt-N&components=buttons&enable-funding=card"
        onLoad={() => setPaypalLoaded(true)}
      />

      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">
            Purchase {selectedPlan.unit === 'verification' ? 'verifications' : 'subscription'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <p className="text-gray-600 mb-6 text-center">
            Select amount of {selectedPlan.unit === 'verification' ? 'verifications' : 'months'} you need
          </p>

          {/* Quantity Selector */}
          <div className="flex items-center justify-center mb-6">
            <button
              onClick={() => handleQuantityChange(quantity - 1)}
              disabled={quantity <= minQuantity}
              className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
            >
              <MinusIcon className="h-5 w-5 text-gray-600" />
            </button>
            
            <div className="mx-8 text-center">
              <div className="text-3xl font-bold text-gray-900 mb-1">
                {quantity} {selectedPlan.unit === 'verification' ? 'Verification' + (quantity > 1 ? 's' : '') : 'Month' + (quantity > 1 ? 's' : '')}
              </div>
            </div>

            <button
              onClick={() => handleQuantityChange(quantity + 1)}
              disabled={quantity >= maxQuantity}
              className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
            >
              <PlusIcon className="h-5 w-5 text-gray-600" />
            </button>
          </div>

          {/* Quantity Range Slider */}
          <div className="mb-6">
            <input
              type="range"
              min={minQuantity}
              max={maxQuantity}
              value={quantity}
              onChange={(e) => handleQuantityChange(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              style={{
                background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${((quantity - minQuantity) / (maxQuantity - minQuantity)) * 100}%, #e5e7eb ${((quantity - minQuantity) / (maxQuantity - minQuantity)) * 100}%, #e5e7eb 100%)`
              }}
            />
            <div className="flex justify-between text-sm text-gray-500 mt-2">
              <span>{minQuantity}{selectedPlan.unit === 'verification' ? 'V' : 'M'}</span>
              <span>{maxQuantity}{selectedPlan.unit === 'verification' ? 'V' : 'M'}</span>
            </div>
          </div>

          {/* Pricing Details */}
          <div className="bg-gray-50 rounded-xl p-4 mb-6">
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-700">Price per {selectedPlan.unit}</span>
              <span className="font-semibold">${pricePerUnit}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-700">Amount of {selectedPlan.unit === 'verification' ? 'verifications' : 'months'}</span>
              <span className="font-semibold">{getQuantityLabel()}</span>
            </div>
            <hr className="my-3 border-gray-200" />
            <div className="flex justify-between items-center">
              <span className="text-lg font-bold text-gray-900">Total price:</span>
              <span className="text-lg font-bold text-gray-900">${totalPrice}</span>
            </div>
          </div>

          {/* Payment Section */}
          {isProcessing ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
              <span className="text-gray-700">Processing payment...</span>
            </div>
          ) : (
            <div className="mb-6">
              <div className="text-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Pay with Card (No PayPal Account Required)</h3>
                <p className="text-sm text-gray-600">Secure payment powered by PayPal</p>
              </div>
              <div id="paypal-button-container" className="min-h-[50px]"></div>
              {!paypalLoaded && (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>
                  <span className="text-gray-600">Loading payment options...</span>
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                // This could trigger the PayPal flow or other payment methods
                console.log(`Continue with ${quantity} ${selectedPlan.unit}(s) for $${totalPrice}`);
              }}
              className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors"
            >
              Continue
            </button>
          </div>

          {/* Additional Info */}
          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500">
              {selectedPlan.unit === 'verification' 
                ? 'Verifications never expire and can be used anytime'
                : 'Subscription will auto-renew unless cancelled'
              }
            </p>
          </div>
        </div>
      </div>
      </div>
    </>
  );
};

export default PaymentModal;
