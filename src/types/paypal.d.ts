declare global {
  interface Window {
    paypal?: {
      Buttons: (config: {
        fundingSource?: any;
        style?: {
          layout?: string;
          shape?: string;
          color?: string;
          label?: string;
        };
        createOrder: (data: any, actions: any) => Promise<string>;
        onApprove: (data: any, actions: any) => Promise<void>;
        onError?: (err: any) => void;
      }) => {
        render: (selector: string) => void;
      };
      FUNDING: {
        CARD: string;
        PAYPAL: string;
      };
    };
  }
}

export {};
